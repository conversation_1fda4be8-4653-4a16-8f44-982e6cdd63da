<?php

/**
 * 数据库初始化类
 * 用于执行数据库迁移和数据填充
 */
class DatabaseSeeder
{
    /**
     * 执行数据库初始化
     */
    public static function run()
    {
        try {
            // 获取数据库连接
            $db = \think\facade\Db::connect();
            
            // 读取SQL文件
            $sqlFile = __DIR__ . '/init_database.sql';
            if (!file_exists($sqlFile)) {
                throw new Exception('SQL文件不存在: ' . $sqlFile);
            }
            
            $sql = file_get_contents($sqlFile);
            
            // 分割SQL语句
            $statements = self::splitSqlStatements($sql);
            
            // 开始事务
            $db->startTrans();
            
            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue;
                }
                
                try {
                    $db->execute($statement);
                    $successCount++;
                    echo "✓ 执行成功: " . substr($statement, 0, 50) . "...\n";
                } catch (Exception $e) {
                    $errorCount++;
                    $error = "✗ 执行失败: " . substr($statement, 0, 50) . "... 错误: " . $e->getMessage();
                    $errors[] = $error;
                    echo $error . "\n";
                }
            }
            
            if ($errorCount > 0) {
                $db->rollback();
                echo "\n数据库初始化失败，已回滚所有更改。\n";
                echo "错误数量: {$errorCount}\n";
                foreach ($errors as $error) {
                    echo $error . "\n";
                }
                return false;
            } else {
                $db->commit();
                echo "\n数据库初始化成功！\n";
                echo "成功执行语句数: {$successCount}\n";
                return true;
            }
            
        } catch (Exception $e) {
            if (isset($db)) {
                $db->rollback();
            }
            echo "数据库初始化失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 分割SQL语句
     * @param string $sql
     * @return array
     */
    private static function splitSqlStatements($sql)
    {
        // 移除注释
        $sql = preg_replace('/--.*$/m', '', $sql);
        
        // 按分号分割，但要考虑字符串中的分号
        $statements = [];
        $current = '';
        $inString = false;
        $stringChar = '';
        
        for ($i = 0; $i < strlen($sql); $i++) {
            $char = $sql[$i];
            
            if (!$inString) {
                if ($char === '"' || $char === "'") {
                    $inString = true;
                    $stringChar = $char;
                } elseif ($char === ';') {
                    $statements[] = trim($current);
                    $current = '';
                    continue;
                }
            } else {
                if ($char === $stringChar && $sql[$i-1] !== '\\') {
                    $inString = false;
                    $stringChar = '';
                }
            }
            
            $current .= $char;
        }
        
        if (!empty(trim($current))) {
            $statements[] = trim($current);
        }
        
        return array_filter($statements, function($stmt) {
            return !empty(trim($stmt));
        });
    }
    
    /**
     * 检查数据库连接
     */
    public static function checkConnection()
    {
        try {
            $db = \think\facade\Db::connect();
            $result = $db->query('SELECT 1');
            echo "数据库连接正常\n";
            return true;
        } catch (Exception $e) {
            echo "数据库连接失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 检查表是否存在
     */
    public static function checkTables()
    {
        $tables = [
            'pp_users',
            'pp_processes', 
            'pp_products',
            'pp_production_orders',
            'pp_production_order_processes',
            'pp_process_images',
            'pp_user_process_permissions',
            'pp_operation_logs'
        ];
        
        try {
            $db = \think\facade\Db::connect();
            $existingTables = [];
            
            foreach ($tables as $table) {
                $result = $db->query("SHOW TABLES LIKE '{$table}'");
                if (!empty($result)) {
                    $existingTables[] = $table;
                }
            }
            
            echo "已存在的表: " . implode(', ', $existingTables) . "\n";
            echo "缺失的表: " . implode(', ', array_diff($tables, $existingTables)) . "\n";
            
            return count($existingTables) === count($tables);
            
        } catch (Exception $e) {
            echo "检查表失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
}
