<?php
// +----------------------------------------------------------------------
// | 包装印刷排产管理系统路由配置
// +----------------------------------------------------------------------
use think\facade\Route;

// 管理端路由
Route::group('admin', function () {
    // 登录相关路由
    Route::get('login', 'admin/Login/index');
    Route::post('login', 'admin/Login/login');
    Route::post('logout', 'admin/Login/logout');

    // 需要登录的路由
    Route::get('/', 'admin/Index/index');
    Route::get('index', 'admin/Index/index');

    // 用户管理
    Route::group('user', function () {
        Route::get('/', 'admin/User/index');
        Route::get('create', 'admin/User/create');
        Route::post('save', 'admin/User/save');
        Route::get('edit/:id', 'admin/User/edit');
        Route::post('update/:id', 'admin/User/update');
        Route::delete('delete/:id', 'admin/User/delete');
        Route::post('status/:id', 'admin/User/status');
    });
});

// 操作员端路由
Route::group('operator', function () {
    // 登录相关路由
    Route::get('login', 'operator/Login/index');
    Route::post('login', 'operator/Login/login');
    Route::post('logout', 'operator/Login/logout');

    // 需要登录的路由
    Route::get('/', 'operator/Index/index');
    Route::get('index', 'operator/Index/index');
});
