-- 工序表
CREATE TABLE `pp_processes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '工序ID',
  `name` varchar(100) NOT NULL COMMENT '工序名称',
  `description` text COMMENT '工序描述',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `estimated_time` int(11) DEFAULT NULL COMMENT '预估耗时（分钟）',
  `difficulty_level` tinyint(4) DEFAULT 1 COMMENT '难度等级（1-5）',
  `quality_standard` text COMMENT '质量标准说明',
  `operation_guide` text COMMENT '操作指南',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工序表';

-- 插入示例工序数据
INSERT INTO `pp_processes` (`name`, `description`, `sort_order`, `status`) VALUES
('设计制版', '根据客户需求进行包装设计和制版', 1, 'active'),
('印刷', '使用印刷机进行批量印刷', 2, 'active'),
('覆膜', '在印刷品表面覆膜，增强防水防污能力', 3, 'active'),
('模切', '按照设计要求进行模切成型', 4, 'active'),
('糊盒', '将模切好的材料糊制成盒子', 5, 'active'),
('质检', '对成品进行质量检验', 6, 'active'),
('包装', '对合格产品进行包装', 7, 'active');
