<?php
declare (strict_types = 1);

namespace app\operator\middleware;

use think\Request;
use think\Response;

/**
 * 操作员认证中间件
 */
class OperatorAuth
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        // 获取当前控制器和方法
        $controller = $request->controller();
        $action = $request->action();
        
        // 不需要验证的控制器和方法
        $allowController = ['Login'];
        $allowAction = ['login', 'logout'];
        
        // 如果是允许的控制器或方法，直接通过
        if (in_array($controller, $allowController) || in_array($action, $allowAction)) {
            return $next($request);
        }
        
        // 检查是否已登录
        if (!is_operator_login()) {
            // AJAX请求返回JSON
            if ($request->isAjax()) {
                return json([
                    'code' => 401,
                    'msg'  => '请先登录',
                    'data' => [],
                ]);
            }
            
            // 普通请求跳转到登录页
            return redirect('/operator/login');
        }
        
        return $next($request);
    }
}
