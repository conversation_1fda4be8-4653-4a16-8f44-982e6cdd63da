<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\model\User;
use app\common\model\OperationLog;
use think\App;
use think\exception\ValidateException;

/**
 * 管理端登录控制器
 */
class Login
{
    protected $request;
    protected $app;

    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;
    }

    /**
     * 登录页面和登录处理
     * @return string|\think\response\Json
     */
    public function index()
    {
        // 如果是POST请求，处理登录
        if ($this->request->isPost()) {
            return $this->login();
        }

        // 如果已经登录，跳转到首页
        if (is_admin_login()) {
            return redirect('/admin/index');
        }

        return $this->renderLoginPage();
    }

    /**
     * 登录处理
     * @return \think\response\Json
     */
    public function login()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        $username = $this->request->post('username', '');
        $password = $this->request->post('password', '');
        $remember = $this->request->post('remember', 0);

        // 验证输入
        if (empty($username) || empty($password)) {
            return json(['code' => 400, 'msg' => '用户名和密码不能为空']);
        }

        try {
            // 查找用户
            $user = User::getByUsername($username);
            if (!$user) {
                return json(['code' => 401, 'msg' => '用户名或密码错误']);
            }

            // 检查用户状态
            if ($user->status !== User::STATUS_ACTIVE) {
                return json(['code' => 403, 'msg' => '账户已被禁用，请联系管理员']);
            }

            // 检查用户角色
            if ($user->role !== User::ROLE_ADMIN) {
                return json(['code' => 403, 'msg' => '无权限访问管理端']);
            }

            // 验证密码
            if (!User::verifyPassword($password, $user->password)) {
                return json(['code' => 401, 'msg' => '用户名或密码错误']);
            }

            // 登录成功，设置会话
            $userData = [
                'id' => $user->id,
                'username' => $user->username,
                'real_name' => $user->real_name,
                'role' => $user->role,
                'avatar' => $user->avatar,
                'login_time' => time(),
            ];

            session('admin_user', $userData);

            // 设置记住登录
            if ($remember) {
                cookie('admin_remember', $user->id, 7 * 24 * 3600); // 7天
            }

            // 更新最后登录信息
            $user->updateLastLogin($this->request->ip());

            // 记录登录日志
            OperationLog::log('user', 'login', '管理员登录', null, $userData);

            return json([
                'code' => 200,
                'msg' => '登录成功',
                'data' => [
                    'redirect' => '/admin/index'
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '登录失败：' . $e->getMessage()]);
        }
    }

    /**
     * 登出处理
     * @return \think\response\Json
     */
    public function logout()
    {
        $user = session('admin_user');
        
        // 清除会话
        session('admin_user', null);
        cookie('admin_remember', null);

        // 记录登出日志
        if ($user) {
            OperationLog::log('user', 'logout', '管理员登出', $user);
        }

        if ($this->request->isAjax()) {
            return json([
                'code' => 200,
                'msg' => '登出成功',
                'data' => [
                    'redirect' => '/admin/login'
                ]
            ]);
        }

        return redirect('/admin/login');
    }

    /**
     * 渲染登录页面
     * @return string
     */
    private function renderLoginPage()
    {
        return '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 包装印刷排产管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .login-container { min-height: 100vh; display: flex; align-items: center; }
        .login-card { background: rgba(255,255,255,0.95); border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
        .login-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; }
        .btn-login { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .btn-login:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container-fluid login-container">
        <div class="row justify-content-center w-100">
            <div class="col-md-4">
                <div class="card login-card">
                    <div class="card-header login-header text-center py-4">
                        <h3><i class="bi bi-shield-lock"></i> 管理员登录</h3>
                        <p class="mb-0">包装印刷排产管理系统</p>
                    </div>
                    <div class="card-body p-5">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-person"></i> 用户名</label>
                                <input type="text" class="form-control" name="username" placeholder="请输入用户名" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-lock"></i> 密码</label>
                                <input type="password" class="form-control" name="password" placeholder="请输入密码" required>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" name="remember" id="remember">
                                <label class="form-check-label" for="remember">记住登录状态</label>
                            </div>
                            <button type="submit" class="btn btn-login btn-primary w-100 py-2">
                                <i class="bi bi-box-arrow-in-right"></i> 登录
                            </button>
                        </form>
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                默认账户：admin / password<br>
                                <a href="/operator/login" class="text-decoration-none">工序操作员登录</a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById("loginForm").addEventListener("submit", function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector("button[type=submit]");
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = "<i class=\"bi bi-hourglass-split\"></i> 登录中...";
            submitBtn.disabled = true;

            fetch("/admin/login", {
                method: "POST",
                body: formData,
                headers: {
                    "X-Requested-With": "XMLHttpRequest"
                }
            })
            .then(response => {
                // 检查响应是否为JSON
                const contentType = response.headers.get("content-type");
                if (contentType && contentType.includes("application/json")) {
                    return response.json();
                } else {
                    // 如果不是JSON，读取文本内容
                    return response.text().then(text => {
                        throw new Error("服务器返回了非JSON响应: " + text.substring(0, 100));
                    });
                }
            })
            .then(data => {
                if (data.code === 200) {
                    alert("登录成功！");
                    window.location.href = data.data.redirect;
                } else {
                    alert(data.msg || "登录失败");
                }
            })
            .catch(error => {
                console.error("Login error:", error);
                alert("登录失败：" + error.message);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>';
    }
}
