<?php
declare (strict_types = 1);

namespace app\common\model;

/**
 * 排产单模型
 */
class ProductionOrder extends BaseModel
{
    // 表名
    protected $name = 'production_orders';

    // 排产单表没有软删除字段
    protected $deleteTime = false;
    
    // 状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_RUNNING = 'running';
    const STATUS_PAUSED = 'paused';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    
    // 状态文本映射
    protected $statusText = [
        self::STATUS_PENDING => '未开始',
        self::STATUS_RUNNING => '进行中',
        self::STATUS_PAUSED => '暂停',
        self::STATUS_COMPLETED => '已完成',
        self::STATUS_CANCELLED => '已取消',
    ];
    
    // 类型转换
    protected $type = [
        'quantity' => 'integer',
        'sort_order' => 'integer',
        'progress' => 'float',
        'total_qualified_quantity' => 'integer',
        'total_defective_quantity' => 'integer',
        'urgent_level' => 'integer',
        'plan_date' => 'date',
        'delivery_date' => 'date',
        'started_at' => 'timestamp',
        'completed_at' => 'timestamp',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp',
    ];
    
    /**
     * 关联产品
     * @return \think\model\relation\BelongsTo
     */
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
    
    /**
     * 关联当前工序
     * @return \think\model\relation\BelongsTo
     */
    public function currentProcess()
    {
        return $this->belongsTo(Process::class, 'current_process_id');
    }
    
    /**
     * 关联工序列表
     * @return \think\model\relation\HasMany
     */
    public function processes()
    {
        return $this->hasMany(ProductionOrderProcess::class, 'production_order_id')
                    ->order('sort_order', 'asc');
    }
    
    /**
     * 获取状态文本
     * @param string $status
     * @return string
     */
    public function getStatusText($status = '')
    {
        $status = $status ?: $this->status;
        return $this->statusText[$status] ?? '未知';
    }
    
    /**
     * 生成排产单号
     * @return string
     */
    public static function generateOrderNo()
    {
        $date = date('Ymd');
        $count = self::where('order_no', 'like', 'PO' . $date . '%')->count();
        return 'PO' . $date . str_pad($count + 1, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * 获取排产单列表（按优先级和时间排序）
     * @param array $where
     * @param int $limit
     * @return \think\Paginator
     */
    public static function getOrderList($where = [], $limit = 15)
    {
        return self::with(['product', 'currentProcess'])
                   ->where($where)
                   ->order(['sort_order' => 'asc', 'urgent_level' => 'desc', 'plan_date' => 'asc'])
                   ->paginate($limit);
    }
    
    /**
     * 获取操作员的排产单列表
     * @param int $operatorId
     * @return \think\Collection
     */
    public static function getOperatorOrders($operatorId)
    {
        return self::alias('po')
                   ->join('production_order_processes pop', 'po.current_process_id = pop.process_id AND po.id = pop.production_order_id')
                   ->join('user_process_permissions upp', 'pop.process_id = upp.process_id')
                   ->where('upp.user_id', $operatorId)
                   ->where('upp.permission_type', 'execute')
                   ->where('po.status', self::STATUS_RUNNING)
                   ->with(['product', 'currentProcess'])
                   ->field('po.*')
                   ->order(['po.sort_order' => 'asc', 'po.urgent_level' => 'desc'])
                   ->select();
    }
    
    /**
     * 开始排产单
     * @return bool
     */
    public function start()
    {
        if ($this->status !== self::STATUS_PENDING) {
            return false;
        }
        
        // 获取第一个工序
        $firstProcess = $this->processes()->where('status', 'pending')->order('sort_order', 'asc')->find();
        if (!$firstProcess) {
            return false;
        }
        
        return $this->save([
            'status' => self::STATUS_RUNNING,
            'current_process_id' => $firstProcess->process_id,
            'started_at' => time(),
        ]);
    }
    
    /**
     * 暂停排产单
     * @return bool
     */
    public function pause()
    {
        if ($this->status !== self::STATUS_RUNNING) {
            return false;
        }
        
        return $this->save(['status' => self::STATUS_PAUSED]);
    }
    
    /**
     * 恢复排产单
     * @return bool
     */
    public function resume()
    {
        if ($this->status !== self::STATUS_PAUSED) {
            return false;
        }
        
        return $this->save(['status' => self::STATUS_RUNNING]);
    }
    
    /**
     * 完成排产单
     * @return bool
     */
    public function complete()
    {
        return $this->save([
            'status' => self::STATUS_COMPLETED,
            'progress' => 100.00,
            'completed_at' => time(),
        ]);
    }
    
    /**
     * 取消排产单
     * @return bool
     */
    public function cancel()
    {
        return $this->save(['status' => self::STATUS_CANCELLED]);
    }
    
    /**
     * 更新进度
     * @return bool
     */
    public function updateProgress()
    {
        $totalProcesses = $this->processes()->count();
        $completedProcesses = $this->processes()->where('status', 'completed')->count();
        
        $progress = $totalProcesses > 0 ? round($completedProcesses / $totalProcesses * 100, 2) : 0;
        
        // 更新总合格数量和不合格数量
        $totalQualified = $this->processes()->sum('qualified_quantity');
        $totalDefective = $this->processes()->sum('defective_quantity');
        
        return $this->save([
            'progress' => $progress,
            'total_qualified_quantity' => $totalQualified,
            'total_defective_quantity' => $totalDefective,
        ]);
    }
    
    /**
     * 流转到下一工序
     * @param int $currentProcessId
     * @return bool
     */
    public function transferToNext($currentProcessId)
    {
        // 获取下一个工序
        $nextProcess = $this->processes()
                           ->where('sort_order', '>', function($query) use ($currentProcessId) {
                               $query->table('production_order_processes')
                                     ->where('production_order_id', $this->id)
                                     ->where('process_id', $currentProcessId)
                                     ->field('sort_order');
                           })
                           ->where('status', 'pending')
                           ->order('sort_order', 'asc')
                           ->find();
        
        if ($nextProcess) {
            // 流转到下一工序
            return $this->save(['current_process_id' => $nextProcess->process_id]);
        } else {
            // 没有下一工序，完成排产单
            return $this->complete();
        }
    }
}
