<?php
declare (strict_types = 1);

namespace app\common\model;

/**
 * 排产单工序模型
 */
class ProductionOrderProcess extends BaseModel
{
    // 表名
    protected $name = 'production_order_processes';

    // 排产单工序表没有软删除字段
    protected $deleteTime = false;
    
    // 状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_RUNNING = 'running';
    const STATUS_COMPLETED = 'completed';
    const STATUS_SKIPPED = 'skipped';
    
    // 状态文本映射
    protected $statusText = [
        self::STATUS_PENDING => '待处理',
        self::STATUS_RUNNING => '进行中',
        self::STATUS_COMPLETED => '已完成',
        self::STATUS_SKIPPED => '跳过',
    ];
    
    // 类型转换
    protected $type = [
        'sort_order' => 'integer',
        'qualified_quantity' => 'integer',
        'defective_quantity' => 'integer',
        'qualification_rate' => 'float',
        'estimated_time' => 'integer',
        'actual_time' => 'integer',
        'started_at' => 'timestamp',
        'completed_at' => 'timestamp',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp',
    ];
    
    /**
     * 关联排产单
     * @return \think\model\relation\BelongsTo
     */
    public function productionOrder()
    {
        return $this->belongsTo(ProductionOrder::class, 'production_order_id');
    }
    
    /**
     * 关联工序
     * @return \think\model\relation\BelongsTo
     */
    public function process()
    {
        return $this->belongsTo(Process::class, 'process_id');
    }
    
    /**
     * 关联操作员
     * @return \think\model\relation\BelongsTo
     */
    public function operator()
    {
        return $this->belongsTo(User::class, 'operator_id');
    }
    
    /**
     * 关联工序图片
     * @return \think\model\relation\HasMany
     */
    public function images()
    {
        return $this->hasMany(ProcessImage::class, 'production_order_process_id')
                    ->order('sort_order', 'asc');
    }
    
    /**
     * 获取状态文本
     * @param string $status
     * @return string
     */
    public function getStatusText($status = '')
    {
        $status = $status ?: $this->status;
        return $this->statusText[$status] ?? '未知';
    }
    
    /**
     * 开始工序
     * @param int $operatorId
     * @return bool
     */
    public function start($operatorId)
    {
        if ($this->status !== self::STATUS_PENDING) {
            return false;
        }
        
        return $this->save([
            'status' => self::STATUS_RUNNING,
            'operator_id' => $operatorId,
            'started_at' => time(),
        ]);
    }
    
    /**
     * 完成工序
     * @param array $data 包含合格数量、问题反馈等
     * @return bool
     */
    public function complete($data)
    {
        if ($this->status !== self::STATUS_RUNNING) {
            return false;
        }
        
        // 计算合格率
        $totalQuantity = $data['qualified_quantity'] + ($data['defective_quantity'] ?? 0);
        $qualificationRate = $totalQuantity > 0 ? 
            round($data['qualified_quantity'] / $totalQuantity * 100, 2) : 0;
        
        // 计算实际耗时
        $actualTime = $this->started_at ? 
            round((time() - strtotime($this->started_at)) / 60) : null;
        
        $updateData = [
            'status' => self::STATUS_COMPLETED,
            'qualified_quantity' => $data['qualified_quantity'],
            'defective_quantity' => $data['defective_quantity'] ?? 0,
            'qualification_rate' => $qualificationRate,
            'feedback' => $data['feedback'] ?? '',
            'actual_time' => $actualTime,
            'completed_at' => time(),
        ];
        
        return $this->save($updateData);
    }
    
    /**
     * 跳过工序
     * @param string $reason
     * @return bool
     */
    public function skip($reason = '')
    {
        if ($this->status === self::STATUS_COMPLETED) {
            return false;
        }
        
        return $this->save([
            'status' => self::STATUS_SKIPPED,
            'feedback' => $reason,
            'completed_at' => time(),
        ]);
    }
    
    /**
     * 获取工序执行统计
     * @param int $processId
     * @return array
     */
    public static function getProcessStatistics($processId)
    {
        $total = self::where('process_id', $processId)->count();
        $completed = self::where('process_id', $processId)
                         ->where('status', self::STATUS_COMPLETED)
                         ->count();
        
        $avgQualificationRate = self::where('process_id', $processId)
                                   ->where('status', self::STATUS_COMPLETED)
                                   ->avg('qualification_rate');
        
        $avgActualTime = self::where('process_id', $processId)
                            ->where('status', self::STATUS_COMPLETED)
                            ->where('actual_time', '>', 0)
                            ->avg('actual_time');
        
        return [
            'total_count' => $total,
            'completed_count' => $completed,
            'completion_rate' => $total > 0 ? round($completed / $total * 100, 2) : 0,
            'avg_qualification_rate' => round($avgQualificationRate ?: 0, 2),
            'avg_actual_time' => round($avgActualTime ?: 0, 2),
        ];
    }
    
    /**
     * 获取操作员的工序任务
     * @param int $operatorId
     * @param string $status
     * @return \think\Collection
     */
    public static function getOperatorTasks($operatorId, $status = '')
    {
        $query = self::alias('pop')
                     ->join('production_orders po', 'pop.production_order_id = po.id')
                     ->join('processes p', 'pop.process_id = p.id')
                     ->join('products prod', 'po.product_id = prod.id')
                     ->join('user_process_permissions upp', 'pop.process_id = upp.process_id')
                     ->where('upp.user_id', $operatorId)
                     ->where('upp.permission_type', 'execute')
                     ->field('pop.*, po.order_no, po.quantity as order_quantity, po.delivery_date, 
                             p.name as process_name, prod.name as product_name, prod.abbreviation');
        
        if ($status) {
            $query->where('pop.status', $status);
        }
        
        return $query->order(['po.sort_order' => 'asc', 'po.urgent_level' => 'desc', 'pop.sort_order' => 'asc'])
                     ->select();
    }
    
    /**
     * 批量创建排产单工序
     * @param int $productionOrderId
     * @param array $processIds
     * @return bool
     */
    public static function createBatch($productionOrderId, $processIds)
    {
        try {
            self::startTrans();
            
            $sortOrder = 1;
            foreach ($processIds as $processId) {
                $process = Process::find($processId);
                if ($process) {
                    self::create([
                        'production_order_id' => $productionOrderId,
                        'process_id' => $processId,
                        'sort_order' => $sortOrder,
                        'estimated_time' => $process->estimated_time,
                    ]);
                    $sortOrder++;
                }
            }
            
            self::commit();
            return true;
        } catch (\Exception $e) {
            self::rollback();
            return false;
        }
    }
}
