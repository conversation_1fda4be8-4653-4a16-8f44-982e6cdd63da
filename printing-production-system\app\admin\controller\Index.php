<?php
declare (strict_types = 1);

namespace app\admin\controller;

/**
 * 管理端首页控制器
 */
class Index extends BaseController
{
    /**
     * 首页
     * @return string
     */
    public function index()
    {
        return '<h1>包装印刷公司排产管理系统 - 管理端</h1>
                <p>ThinkPHP 8.0 框架搭建成功！</p>
                <p>当前时间：' . date('Y-m-d H:i:s') . '</p>
                <hr>
                <h3>功能模块：</h3>
                <ul>
                    <li>用户管理</li>
                    <li>工序管理</li>
                    <li>产品管理</li>
                    <li>排产管理</li>
                    <li>大数据看板</li>
                </ul>';
    }
}
