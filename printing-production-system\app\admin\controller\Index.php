<?php
declare (strict_types = 1);

namespace app\admin\controller;

/**
 * 管理端首页控制器
 */
class Index extends BaseController
{
    /**
     * 首页
     * @return string
     */
    public function index()
    {
        $user = get_admin_user();
        return $this->renderDashboard($user);
    }

    /**
     * 渲染管理端仪表板
     * @param array $user
     * @return string
     */
    private function renderDashboard($user)
    {
        return '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理端首页 - 包装印刷排产管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .sidebar .nav-link { color: rgba(255,255,255,0.8); }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: white; background: rgba(255,255,255,0.1); }
        .main-content { background: #f8f9fa; min-height: 100vh; }
        .card-stats { border-left: 4px solid; }
        .card-stats.primary { border-left-color: #667eea; }
        .card-stats.success { border-left-color: #00b894; }
        .card-stats.warning { border-left-color: #fdcb6e; }
        .card-stats.danger { border-left-color: #e17055; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-0">
                <div class="p-3 text-white">
                    <h5><i class="bi bi-gear-wide-connected"></i> 管理端</h5>
                    <small>欢迎，' . htmlspecialchars($user['real_name'] ?: $user['username']) . '</small>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link active" href="/admin/index">
                        <i class="bi bi-house"></i> 首页
                    </a>
                    <a class="nav-link" href="/admin/user">
                        <i class="bi bi-people"></i> 用户管理
                    </a>
                    <a class="nav-link" href="/admin/process">
                        <i class="bi bi-diagram-3"></i> 工序管理
                    </a>
                    <a class="nav-link" href="/admin/product">
                        <i class="bi bi-box"></i> 产品管理
                    </a>
                    <a class="nav-link" href="/admin/production">
                        <i class="bi bi-clipboard-data"></i> 排产管理
                    </a>
                    <a class="nav-link" href="/admin/dashboard">
                        <i class="bi bi-graph-up"></i> 数据看板
                    </a>
                    <hr class="text-white-50">
                    <a class="nav-link" href="javascript:void(0)" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> 退出登录
                    </a>
                </nav>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>系统概览</h2>
                    <span class="text-muted">' . date('Y-m-d H:i:s') . '</span>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card card-stats primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title text-muted">用户总数</h5>
                                        <h3 class="mb-0">4</h3>
                                    </div>
                                    <div class="text-primary">
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card card-stats success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title text-muted">工序数量</h5>
                                        <h3 class="mb-0">7</h3>
                                    </div>
                                    <div class="text-success">
                                        <i class="bi bi-diagram-3 fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card card-stats warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title text-muted">产品种类</h5>
                                        <h3 class="mb-0">7</h3>
                                    </div>
                                    <div class="text-warning">
                                        <i class="bi bi-box fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card card-stats danger">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title text-muted">排产单</h5>
                                        <h3 class="mb-0">3</h3>
                                    </div>
                                    <div class="text-danger">
                                        <i class="bi bi-clipboard-data fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-lightning"></i> 快速操作</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="/admin/user/create" class="btn btn-outline-primary">
                                        <i class="bi bi-person-plus"></i> 添加用户
                                    </a>
                                    <a href="/admin/product/create" class="btn btn-outline-success">
                                        <i class="bi bi-plus-circle"></i> 添加产品
                                    </a>
                                    <a href="/admin/production/create" class="btn btn-outline-warning">
                                        <i class="bi bi-clipboard-plus"></i> 创建排产单
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-info-circle"></i> 系统信息</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>框架版本：</strong> ThinkPHP 8.0</p>
                                <p><strong>PHP版本：</strong> ' . PHP_VERSION . '</p>
                                <p><strong>数据库：</strong> MySQL</p>
                                <p><strong>当前用户：</strong> ' . htmlspecialchars($user['real_name'] ?: $user['username']) . '</p>
                                <p><strong>登录时间：</strong> ' . date('Y-m-d H:i:s', $user['login_time']) . '</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function logout() {
            if (confirm("确定要退出登录吗？")) {
                fetch("/admin/logout", {
                    method: "POST"
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        window.location.href = data.data.redirect;
                    } else {
                        alert(data.msg);
                    }
                })
                .catch(error => {
                    alert("退出失败：" + error.message);
                });
            }
        }
    </script>
</body>
</html>';
    }
}
