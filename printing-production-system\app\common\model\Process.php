<?php
declare (strict_types = 1);

namespace app\common\model;

/**
 * 工序模型
 */
class Process extends BaseModel
{
    // 表名
    protected $name = 'processes';
    
    // 类型转换
    protected $type = [
        'sort_order' => 'integer',
        'estimated_time' => 'integer',
        'difficulty_level' => 'integer',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp',
        'deleted_at' => 'timestamp',
    ];
    
    /**
     * 获取启用的工序列表（按排序）
     * @return \think\Collection
     */
    public static function getActiveList()
    {
        return self::where('status', self::STATUS_ACTIVE)
                   ->where('deleted_at', null)
                   ->order('sort_order', 'asc')
                   ->select();
    }
    
    /**
     * 获取工序选项（用于下拉框）
     * @return array
     */
    public static function getProcessOptions()
    {
        $list = self::getActiveList();
        $options = [];
        foreach ($list as $item) {
            $options[$item->id] = $item->name;
        }
        return $options;
    }
    
    /**
     * 获取下一个排序号
     * @return int
     */
    public static function getNextSortOrder()
    {
        $maxSort = self::where('deleted_at', null)->max('sort_order');
        return $maxSort ? $maxSort + 1 : 1;
    }
    
    /**
     * 更新排序
     * @param array $sortData 格式：[['id' => 1, 'sort_order' => 1], ...]
     * @return bool
     */
    public static function updateSort($sortData)
    {
        try {
            self::startTrans();
            foreach ($sortData as $item) {
                self::where('id', $item['id'])->update(['sort_order' => $item['sort_order']]);
            }
            self::commit();
            return true;
        } catch (\Exception $e) {
            self::rollback();
            return false;
        }
    }
    
    /**
     * 获取工序的操作员
     * @return \think\Collection
     */
    public function getOperators()
    {
        return User::alias('u')
                   ->join('user_process_permissions upp', 'u.id = upp.user_id')
                   ->where('upp.process_id', $this->id)
                   ->where('upp.permission_type', 'execute')
                   ->where('u.role', User::ROLE_OPERATOR)
                   ->where('u.status', User::STATUS_ACTIVE)
                   ->where('u.deleted_at', null)
                   ->field('u.*')
                   ->select();
    }
    
    /**
     * 检查工序是否可以删除
     * @return bool
     */
    public function canDelete()
    {
        // 检查是否有排产单正在使用此工序
        $count = ProductionOrderProcess::where('process_id', $this->id)->count();
        return $count === 0;
    }
    
    /**
     * 获取工序统计信息
     * @return array
     */
    public function getStatistics()
    {
        // 获取使用此工序的排产单数量
        $totalOrders = ProductionOrderProcess::where('process_id', $this->id)->count();
        
        // 获取已完成的工序数量
        $completedOrders = ProductionOrderProcess::where('process_id', $this->id)
                                                 ->where('status', 'completed')
                                                 ->count();
        
        // 获取平均合格率
        $avgQualificationRate = ProductionOrderProcess::where('process_id', $this->id)
                                                      ->where('status', 'completed')
                                                      ->avg('qualification_rate');
        
        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'completion_rate' => $totalOrders > 0 ? round($completedOrders / $totalOrders * 100, 2) : 0,
            'avg_qualification_rate' => round($avgQualificationRate ?: 0, 2),
        ];
    }
}
