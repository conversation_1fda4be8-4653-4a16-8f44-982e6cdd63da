<?php
// 工序操作员端路由配置

use think\facade\Route;

// 登录相关路由
Route::group('', function () {
    Route::get('login', 'Login/index');
    Route::post('login', 'Login/login');
    Route::post('logout', 'Login/logout');
});

// 需要登录的路由组
Route::group('', function () {
    // 首页
    Route::get('/', 'Index/index');
    Route::get('index', 'Index/index');
    
    // 排产单管理
    Route::group('production', function () {
        Route::get('/', 'Production/index');
        Route::get('detail/:id', 'Production/detail');
        Route::get('process/:id/:processId', 'Production/process');
        Route::post('complete/:id/:processId', 'Production/complete');
        Route::post('transfer/:id/:processId', 'Production/transfer');
    });
    
    // 工序操作
    Route::group('process', function () {
        Route::get('list', 'Process/list');
        Route::get('detail/:id', 'Process/detail');
        Route::post('upload/:id', 'Process/upload');
    });
    
    // 文件上传
    Route::post('upload', 'Upload/index');
    
});

return [];
