<?php
declare (strict_types = 1);

namespace app\admin\controller;

use think\App;
use think\exception\ValidateException;
use think\Validate;

/**
 * 管理端基础控制器类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {
        // 暂时注释掉登录检查，用于测试框架
        // if (!$this->checkLogin()) {
        //     $this->redirect('/admin/login');
        // }
    }

    /**
     * 检查登录状态
     * @return bool
     */
    protected function checkLogin()
    {
        // 登录页面和登录接口不需要检查
        $controller = $this->request->controller();
        $action = $this->request->action();
        
        if ($controller === 'Login' || ($controller === 'Index' && $action === 'login')) {
            return true;
        }
        
        return is_admin_login();
    }

    /**
     * 验证数据
     * @access protected
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 成功返回
     * @param string $msg 提示信息
     * @param array $data 返回数据
     * @param int $code 状态码
     * @return \think\response\Json
     */
    protected function success($msg = 'success', $data = [], $code = 200)
    {
        return json([
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
        ]);
    }

    /**
     * 失败返回
     * @param string $msg 提示信息
     * @param array $data 返回数据
     * @param int $code 状态码
     * @return \think\response\Json
     */
    protected function error($msg = 'error', $data = [], $code = 400)
    {
        return json([
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
        ]);
    }
}
