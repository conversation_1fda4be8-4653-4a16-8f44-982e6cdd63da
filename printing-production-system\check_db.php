<?php
require_once 'vendor/autoload.php';

$app = new \think\App();
$app->initialize();

try {
    $db = \think\facade\Db::connect();
    
    echo "===========================================\n";
    echo "数据库状态检查\n";
    echo "===========================================\n\n";
    
    // 检查表是否存在
    $tables = [
        'pp_users' => '用户表',
        'pp_processes' => '工序表', 
        'pp_products' => '产品表',
        'pp_production_orders' => '排产单表',
        'pp_production_order_processes' => '排产单工序表',
        'pp_process_images' => '工序图片表',
        'pp_user_process_permissions' => '用户工序权限表',
        'pp_operation_logs' => '操作日志表'
    ];
    
    foreach ($tables as $table => $name) {
        try {
            $count = $db->table($table)->count();
            echo "✓ {$name} ({$table}): {$count} 条记录\n";
        } catch (Exception $e) {
            echo "✗ {$name} ({$table}): 表不存在\n";
        }
    }
    
    echo "\n数据库连接正常，表结构完整！\n";
    
    // 显示一些示例数据
    echo "\n示例数据:\n";
    echo "-------------------------------------------\n";
    
    // 用户数据
    $users = $db->table('pp_users')->field('username,real_name,role')->select();
    echo "用户列表:\n";
    foreach ($users as $user) {
        echo "- {$user['username']} ({$user['real_name']}) - {$user['role']}\n";
    }
    
    // 工序数据
    $processes = $db->table('pp_processes')->field('name,sort_order')->order('sort_order')->select();
    echo "\n工序列表:\n";
    foreach ($processes as $process) {
        echo "- {$process['sort_order']}. {$process['name']}\n";
    }
    
    // 产品数据
    $products = $db->table('pp_products')->field('name,abbreviation')->select();
    echo "\n产品列表:\n";
    foreach ($products as $product) {
        echo "- {$product['abbreviation']}: {$product['name']}\n";
    }
    
    echo "\n===========================================\n";
    echo "数据库第二阶段初始化完成！\n";
    echo "===========================================\n";
    
} catch (Exception $e) {
    echo "检查失败: " . $e->getMessage() . "\n";
}
