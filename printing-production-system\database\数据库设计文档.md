# 包装印刷公司排产管理系统 - 数据库设计文档

## 概述

本文档详细描述了包装印刷公司排产管理系统的数据库设计，包括表结构、字段说明、索引设计和数据关系。

**数据库名称**: `printing_production`  
**字符集**: `utf8mb4`  
**排序规则**: `utf8mb4_unicode_ci`  
**表前缀**: `pp_`

## 数据库表结构

### 1. 用户表 (pp_users)

存储系统用户信息，包括管理员和工序操作员。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | AUTO_INCREMENT | 用户ID（主键） |
| username | varchar | 50 | NO | - | 用户名（唯一） |
| password | varchar | 255 | NO | - | 密码（加密） |
| real_name | varchar | 100 | YES | NULL | 真实姓名 |
| role | enum | - | NO | operator | 角色：admin-管理员，operator-操作员 |
| status | enum | - | NO | active | 状态：active-启用，inactive-停用 |
| avatar | varchar | 255 | YES | NULL | 头像路径 |
| phone | varchar | 20 | YES | NULL | 手机号 |
| email | varchar | 100 | YES | NULL | 邮箱 |
| last_login_time | timestamp | - | YES | NULL | 最后登录时间 |
| last_login_ip | varchar | 45 | YES | NULL | 最后登录IP |
| created_at | timestamp | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | NO | CURRENT_TIMESTAMP | 更新时间 |
| deleted_at | timestamp | - | YES | NULL | 删除时间（软删除） |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `username`
- KEY: `role`, `status`, `deleted_at`

### 2. 工序表 (pp_processes)

存储生产工序信息。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | AUTO_INCREMENT | 工序ID（主键） |
| name | varchar | 100 | NO | - | 工序名称 |
| description | text | - | YES | NULL | 工序描述 |
| sort_order | int | 11 | NO | 0 | 排序（数字越小越靠前） |
| status | enum | - | NO | active | 状态：active-启用，inactive-停用 |
| estimated_time | int | 11 | YES | NULL | 预估耗时（分钟） |
| difficulty_level | tinyint | 4 | YES | 1 | 难度等级（1-5） |
| quality_standard | text | - | YES | NULL | 质量标准说明 |
| operation_guide | text | - | YES | NULL | 操作指南 |
| created_at | timestamp | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | NO | CURRENT_TIMESTAMP | 更新时间 |
| deleted_at | timestamp | - | YES | NULL | 删除时间（软删除） |

**索引**:
- PRIMARY KEY: `id`
- KEY: `sort_order`, `status`, `deleted_at`

### 3. 产品表 (pp_products)

存储产品信息。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | AUTO_INCREMENT | 产品ID（主键） |
| name | varchar | 200 | NO | - | 产品名称 |
| description | text | - | YES | NULL | 产品描述 |
| abbreviation | varchar | 20 | NO | - | 产品缩写（唯一） |
| category | varchar | 50 | YES | NULL | 产品分类 |
| specifications | varchar | 200 | YES | NULL | 产品规格 |
| material | varchar | 100 | YES | NULL | 材质 |
| color | varchar | 50 | YES | NULL | 颜色 |
| unit | varchar | 20 | YES | 个 | 计量单位 |
| standard_price | decimal | 10,2 | YES | NULL | 标准单价 |
| status | enum | - | NO | active | 状态：active-启用，inactive-停用 |
| image_path | varchar | 255 | YES | NULL | 产品图片路径 |
| remark | text | - | YES | NULL | 备注 |
| created_at | timestamp | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | NO | CURRENT_TIMESTAMP | 更新时间 |
| deleted_at | timestamp | - | YES | NULL | 删除时间（软删除） |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `abbreviation`
- KEY: `name`, `category`, `status`, `deleted_at`

### 4. 排产单表 (pp_production_orders)

存储排产单信息。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | AUTO_INCREMENT | 排产单ID（主键） |
| order_no | varchar | 50 | NO | - | 排产单号（唯一） |
| product_id | int | 11 | NO | - | 产品ID（外键） |
| quantity | int | 11 | NO | - | 排产数量 |
| plan_date | date | - | NO | - | 计划开始时间 |
| delivery_date | date | - | NO | - | 交货时间 |
| sort_order | int | 11 | NO | 0 | 排序（数字越小优先级越高） |
| status | enum | - | NO | pending | 状态：pending-未开始，running-进行中，paused-暂停，completed-完成，cancelled-取消 |
| current_process_id | int | 11 | YES | NULL | 当前工序ID（外键） |
| progress | decimal | 5,2 | YES | 0.00 | 完成进度（百分比） |
| total_qualified_quantity | int | 11 | YES | 0 | 总合格数量 |
| total_defective_quantity | int | 11 | YES | 0 | 总不合格数量 |
| customer_name | varchar | 100 | YES | NULL | 客户名称 |
| customer_contact | varchar | 100 | YES | NULL | 客户联系方式 |
| urgent_level | tinyint | 4 | YES | 1 | 紧急程度（1-5，5最紧急） |
| remark | text | - | YES | NULL | 备注 |
| started_at | timestamp | - | YES | NULL | 实际开始时间 |
| completed_at | timestamp | - | YES | NULL | 实际完成时间 |
| created_at | timestamp | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | NO | CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `order_no`
- KEY: `product_id`, `current_process_id`, `status`, `plan_date`, `delivery_date`, `sort_order`, `urgent_level`

**外键约束**:
- `product_id` → `pp_products.id`
- `current_process_id` → `pp_processes.id`

### 5. 排产单工序表 (pp_production_order_processes)

存储排产单的工序执行信息。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | AUTO_INCREMENT | ID（主键） |
| production_order_id | int | 11 | NO | - | 排产单ID（外键） |
| process_id | int | 11 | NO | - | 工序ID（外键） |
| sort_order | int | 11 | NO | - | 工序排序 |
| status | enum | - | NO | pending | 状态：pending-待处理，running-进行中，completed-已完成，skipped-跳过 |
| qualified_quantity | int | 11 | YES | 0 | 合格数量 |
| defective_quantity | int | 11 | YES | 0 | 不合格数量 |
| qualification_rate | decimal | 5,2 | YES | 0.00 | 合格率（百分比） |
| feedback | text | - | YES | NULL | 问题反馈 |
| operator_id | int | 11 | YES | NULL | 操作员ID（外键） |
| estimated_time | int | 11 | YES | NULL | 预估耗时（分钟） |
| actual_time | int | 11 | YES | NULL | 实际耗时（分钟） |
| started_at | timestamp | - | YES | NULL | 开始时间 |
| completed_at | timestamp | - | YES | NULL | 完成时间 |
| created_at | timestamp | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | NO | CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `production_order_id`, `process_id`
- KEY: `production_order_id`, `process_id`, `operator_id`, `status`, `sort_order`

**外键约束**:
- `production_order_id` → `pp_production_orders.id`
- `process_id` → `pp_processes.id`
- `operator_id` → `pp_users.id`

### 6. 工序图片表 (pp_process_images)

存储工序执行过程中的图片。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | AUTO_INCREMENT | ID（主键） |
| production_order_process_id | int | 11 | NO | - | 排产单工序ID（外键） |
| image_path | varchar | 500 | NO | - | 图片路径 |
| image_name | varchar | 200 | YES | NULL | 图片名称 |
| file_size | int | 11 | YES | NULL | 文件大小（字节） |
| image_type | varchar | 20 | YES | NULL | 图片类型 |
| width | int | 11 | YES | NULL | 图片宽度 |
| height | int | 11 | YES | NULL | 图片高度 |
| description | varchar | 500 | YES | NULL | 图片描述 |
| sort_order | int | 11 | YES | 0 | 排序 |
| created_at | timestamp | - | NO | CURRENT_TIMESTAMP | 创建时间 |

**索引**:
- PRIMARY KEY: `id`
- KEY: `production_order_process_id`, `sort_order`

**外键约束**:
- `production_order_process_id` → `pp_production_order_processes.id`

### 7. 用户工序权限表 (pp_user_process_permissions)

存储用户对工序的操作权限。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | AUTO_INCREMENT | ID（主键） |
| user_id | int | 11 | NO | - | 用户ID（外键） |
| process_id | int | 11 | NO | - | 工序ID（外键） |
| permission_type | enum | - | NO | execute | 权限类型：read-查看，write-编辑，execute-执行 |
| created_at | timestamp | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | NO | CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `user_id`, `process_id`, `permission_type`
- KEY: `user_id`, `process_id`

**外键约束**:
- `user_id` → `pp_users.id`
- `process_id` → `pp_processes.id`

### 8. 操作日志表 (pp_operation_logs)

记录系统操作日志。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | AUTO_INCREMENT | ID（主键） |
| user_id | int | 11 | YES | NULL | 操作用户ID（外键） |
| module | varchar | 50 | NO | - | 操作模块 |
| action | varchar | 50 | NO | - | 操作动作 |
| target_type | varchar | 50 | YES | NULL | 目标类型 |
| target_id | int | 11 | YES | NULL | 目标ID |
| description | varchar | 500 | YES | NULL | 操作描述 |
| old_data | json | - | YES | NULL | 操作前数据 |
| new_data | json | - | YES | NULL | 操作后数据 |
| ip_address | varchar | 45 | YES | NULL | IP地址 |
| user_agent | varchar | 500 | YES | NULL | 用户代理 |
| created_at | timestamp | - | NO | CURRENT_TIMESTAMP | 创建时间 |

**索引**:
- PRIMARY KEY: `id`
- KEY: `user_id`, `module`, `action`, `target_type`, `target_id`, `created_at`

**外键约束**:
- `user_id` → `pp_users.id`

## 数据关系图

```
pp_users (用户表)
├── pp_production_order_processes.operator_id (操作员)
├── pp_user_process_permissions.user_id (权限)
└── pp_operation_logs.user_id (日志)

pp_processes (工序表)
├── pp_production_orders.current_process_id (当前工序)
├── pp_production_order_processes.process_id (工序执行)
└── pp_user_process_permissions.process_id (权限)

pp_products (产品表)
└── pp_production_orders.product_id (排产产品)

pp_production_orders (排产单表)
└── pp_production_order_processes.production_order_id (工序执行)

pp_production_order_processes (排产单工序表)
└── pp_process_images.production_order_process_id (工序图片)
```

## 初始数据

### 默认用户
- 管理员: `admin` / `password`
- 操作员: `operator1` / `password`
- 操作员: `operator2` / `password`
- 操作员: `operator3` / `password`

### 标准工序流程
1. 设计制版
2. 印刷
3. 覆膜
4. 模切
5. 糊盒
6. 质检
7. 包装

### 示例产品
- 高档化妆品包装盒 (HZHZP001)
- 食品包装袋 (SPBZD001)
- 电子产品包装盒 (DZCP001)
- 药品包装盒 (YPBZ001)
- 礼品包装袋 (LPBZD001)
- 茶叶包装盒 (CYBZ001)
- 酒类包装盒 (JLBZ001)

## 使用说明

### 数据库初始化
```bash
# 方法1: 直接导入SQL文件
mysql -u root -p printing_production < database/init_database.sql

# 方法2: 使用PHP脚本
php database/init_db.php
```

### 备份与恢复
```bash
# 备份数据库
mysqldump -u root -p printing_production > backup.sql

# 恢复数据库
mysql -u root -p printing_production < backup.sql
```

---

**文档版本**: v1.0  
**创建时间**: 2025-07-08  
**数据库版本**: MySQL 8.0+
