-- 工序图片表
CREATE TABLE `pp_process_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `production_order_process_id` int(11) NOT NULL COMMENT '排产单工序ID',
  `image_path` varchar(500) NOT NULL COMMENT '图片路径',
  `image_name` varchar(200) DEFAULT NULL COMMENT '图片名称',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小（字节）',
  `image_type` varchar(20) DEFAULT NULL COMMENT '图片类型（jpg, png, gif等）',
  `width` int(11) DEFAULT NULL COMMENT '图片宽度',
  `height` int(11) DEFAULT NULL COMMENT '图片高度',
  `description` varchar(500) DEFAULT NULL COMMENT '图片描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_production_order_process_id` (`production_order_process_id`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_process_images_order_process` FOREIGN KEY (`production_order_process_id`) REFERENCES `pp_production_order_processes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工序图片表';
