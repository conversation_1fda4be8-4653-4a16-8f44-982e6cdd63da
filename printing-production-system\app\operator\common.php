<?php
// 工序操作员端公共函数文件

/**
 * 检查操作员权限
 * @param int $processId 工序ID
 * @return bool
 */
function check_operator_permission($processId = 0)
{
    $operator = get_operator_user();
    if (!$operator) {
        return false;
    }
    
    // 检查操作员是否有该工序的操作权限
    // 这里可以实现具体的权限检查逻辑
    return true;
}

/**
 * 获取当前登录操作员信息
 * @return array|null
 */
function get_operator_user()
{
    return session('operator_user');
}

/**
 * 操作员登录检查
 * @return bool
 */
function is_operator_login()
{
    return !empty(session('operator_user'));
}

/**
 * 计算合格率
 * @param int $qualifiedQuantity 合格数量
 * @param int $totalQuantity 总数量
 * @return float
 */
function calculate_qualification_rate($qualifiedQuantity, $totalQuantity)
{
    if ($totalQuantity <= 0) {
        return 0;
    }
    
    return round(($qualifiedQuantity / $totalQuantity) * 100, 2);
}
