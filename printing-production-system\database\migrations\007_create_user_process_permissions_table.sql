-- 用户工序权限表
CREATE TABLE `pp_user_process_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `process_id` int(11) NOT NULL COMMENT '工序ID',
  `permission_type` enum('read','write','execute') NOT NULL DEFAULT 'execute' COMMENT '权限类型：read-查看，write-编辑，execute-执行',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_process_permission` (`user_id`, `process_id`, `permission_type`),
  KEY `idx_user_id` (`user_id`),
  <PERSON>EY `idx_process_id` (`process_id`),
  CONSTRAINT `fk_user_process_permissions_user` FOREIGN KEY (`user_id`) REFERENCES `pp_users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_user_process_permissions_process` FOREIGN KEY (`process_id`) REFERENCES `pp_processes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户工序权限表';
