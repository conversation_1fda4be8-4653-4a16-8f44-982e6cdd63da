<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 基础模型类
 */
abstract class BaseModel extends Model
{
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段格式
    protected $dateFormat = 'Y-m-d H:i:s';
    
    // 软删除
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'deleted_at';
    protected $defaultSoftDelete = null;
    
    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    
    /**
     * 状态文本映射
     * @var array
     */
    protected $statusText = [
        self::STATUS_ACTIVE   => '启用',
        self::STATUS_INACTIVE => '停用',
    ];
    
    /**
     * 获取状态文本
     * @param string $status
     * @return string
     */
    public function getStatusText($status = '')
    {
        $status = $status ?: $this->status;
        return $this->statusText[$status] ?? '未知';
    }
    
    /**
     * 状态修改器
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusAttr($value)
    {
        return $value;
    }
    
    /**
     * 创建时间获取器
     * @param $value
     * @return string
     */
    public function getCreatedAtAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', strtotime($value)) : '';
    }
    
    /**
     * 更新时间获取器
     * @param $value
     * @return string
     */
    public function getUpdatedAtAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', strtotime($value)) : '';
    }
    
    /**
     * 分页查询
     * @param array $where 查询条件
     * @param int $limit 每页数量
     * @param array $order 排序
     * @return \think\Paginator
     */
    public function getList($where = [], $limit = 15, $order = [])
    {
        $query = $this->where($where);
        
        if (!empty($order)) {
            $query->order($order);
        }
        
        return $query->paginate($limit);
    }
    
    /**
     * 获取所有记录
     * @param array $where 查询条件
     * @param array $order 排序
     * @return \think\Collection
     */
    public function getAll($where = [], $order = [])
    {
        $query = $this->where($where);
        
        if (!empty($order)) {
            $query->order($order);
        }
        
        return $query->select();
    }
    
    /**
     * 根据ID获取记录
     * @param int $id
     * @return static|null
     */
    public function getById($id)
    {
        return $this->find($id);
    }
    
    /**
     * 更新状态
     * @param int $id
     * @param string $status
     * @return bool
     */
    public function updateStatus($id, $status)
    {
        return $this->where('id', $id)->update(['status' => $status]);
    }
}
