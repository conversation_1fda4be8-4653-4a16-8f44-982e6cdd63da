# 包装印刷公司排产管理系统需求文档

## 项目概述

**项目名称**: 包装印刷公司排产管理系统  
**开发框架**: ThinkPHP 8.0  
**前端技术**: H5 + Bootstrap 5 (响应式设计)  
**数据库**: MySQL 8.0+  
**项目类型**: 双端系统（管理端 + 工序操作员端）

## 业务背景

包装印刷公司主要经营各种包装制品的印刷业务，需要一套完整的排产管理系统来管理生产流程、工序操作、质量控制和数据统计。

## 核心业务流程

```
产品定义 → 工序配置 → 排产计划 → 工序执行 → 质量控制 → 完工入库
```

## 系统架构设计

### 技术架构
```
前端层：H5 + Bootstrap 5（响应式设计）
├── 管理端（Admin Panel）
└── 工序操作员端（Operator Panel）

应用层：ThinkPHP 8.0
├── 控制器层（Controller）
├── 服务层（Service）
├── 模型层（Model）
└── 中间件（Middleware）

数据层：MySQL 8.0+
├── 核心业务表
├── 日志表
└── 文件存储表
```

### 模块架构
```
系统模块划分：
├── 用户管理模块（User Management）
├── 工序管理模块（Process Management）
├── 产品管理模块（Product Management）
├── 排产管理模块（Production Planning）
├── 工序执行模块（Process Execution）
├── 大数据看板模块（Dashboard）
└── 文件管理模块（File Management）
```

## 功能模块需求

### 1. 用户管理模块
- **角色类型**: 管理员、工序操作员
- **基础功能**: 用户认证、权限管理、用户CRUD
- **安全要求**: 密码加密、会话管理、权限控制

### 2. 工序管理模块（管理端）
- **核心字段**:
  - 工序名称（必填）
  - 工序描述
  - 工序排序（数字）
  - 工序状态（启用/停用）
  - 工序操作员分配
  - 创建时间、更新时间
- **功能要求**:
  - 工序CRUD操作
  - 工序状态管理
  - 操作员分配管理
  - 软删除功能
  - 工序排序调整

### 3. 产品管理模块（管理端）
- **核心字段**:
  - 产品名称（必填）
  - 产品描述
  - 产品缩写（用于快速索引）
  - 产品状态（启用/停用）
  - 创建时间、更新时间
- **功能要求**:
  - 产品CRUD操作
  - 产品状态管理
  - 软删除功能
  - 产品缩写快速搜索

### 4. 排产管理模块（管理端）
- **排产单字段**:
  - 产品ID（通过产品缩写快速添加）
  - 排产数量（必填）
  - 排产时间（计划开始时间）
  - 交货时间（必填）
  - 排产单排序
  - 状态（未启用/进行中/暂停/完成）
  - 当前工序ID
- **工序流程配置**:
  - 为排产单指定工序流程
  - 工序排序设置
  - 支持部分工序选择
- **功能要求**:
  - 排产单CRUD操作
  - 工序流程配置
  - 状态跟踪管理
  - 产品快速选择

### 5. 工序操作员端功能
- **排产单查看**:
  - 显示需要当前工序的所有排产单
  - 排产数量、排产日期、交货时间
  - 工序流程可视化显示
- **工序状态显示**:
  - 已完成工序（正常显示，可点击查看详情）
  - 当前工序（高亮显示）
  - 未完成工序（灰度显示）
- **工序操作功能**:
  - 合格数量录入
  - 工序问题反馈
  - 多图片上传
  - 工序完成确认
  - 流转到下一工序
- **特殊处理**:
  - 最后工序完成后，排产单自动完结
  - 产品自动入库

### 6. 大数据看板模块（管理端）
- **实时数据展示**:
  - 所有正在排产的产品
  - 排产单列表和状态
  - 排产单完成进度
- **质量数据统计**:
  - 每个工序的合格数量
  - 合格率统计
  - 工序问题反馈汇总
- **可视化展示**:
  - 工序照片展示
  - 图表统计
  - 进度条显示

## 数据库设计

### 核心数据表

#### 1. 用户表（users）
```sql
id              INT PRIMARY KEY AUTO_INCREMENT
username        VARCHAR(50) UNIQUE NOT NULL     -- 用户名
password        VARCHAR(255) NOT NULL           -- 密码（加密）
real_name       VARCHAR(100)                    -- 真实姓名
role            ENUM('admin','operator')        -- 角色
status          ENUM('active','inactive')       -- 状态
created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
deleted_at      TIMESTAMP NULL                  -- 软删除
```

#### 2. 工序表（processes）
```sql
id              INT PRIMARY KEY AUTO_INCREMENT
name            VARCHAR(100) NOT NULL           -- 工序名称
description     TEXT                            -- 工序描述
sort_order      INT DEFAULT 0                   -- 排序
status          ENUM('active','inactive')       -- 状态
created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
deleted_at      TIMESTAMP NULL                  -- 软删除
```

#### 3. 产品表（products）
```sql
id              INT PRIMARY KEY AUTO_INCREMENT
name            VARCHAR(200) NOT NULL           -- 产品名
description     TEXT                            -- 产品描述
abbreviation    VARCHAR(20) UNIQUE              -- 产品缩写
status          ENUM('active','inactive')       -- 状态
created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
deleted_at      TIMESTAMP NULL                  -- 软删除
```

#### 4. 排产单表（production_orders）
```sql
id                  INT PRIMARY KEY AUTO_INCREMENT
product_id          INT NOT NULL                    -- 产品ID
quantity            INT NOT NULL                    -- 排产数量
plan_date           DATE NOT NULL                   -- 排产时间
delivery_date       DATE NOT NULL                   -- 交货时间
sort_order          INT DEFAULT 0                   -- 排序
status              ENUM('pending','running','paused','completed') -- 状态
current_process_id  INT NULL                        -- 当前工序ID
created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

FOREIGN KEY (product_id) REFERENCES products(id)
FOREIGN KEY (current_process_id) REFERENCES processes(id)
```

#### 5. 排产单工序表（production_order_processes）
```sql
id                      INT PRIMARY KEY AUTO_INCREMENT
production_order_id     INT NOT NULL                -- 排产单ID
process_id              INT NOT NULL                -- 工序ID
sort_order              INT NOT NULL                -- 工序排序
status                  ENUM('pending','running','completed') -- 状态
qualified_quantity      INT DEFAULT 0               -- 合格数量
qualification_rate      DECIMAL(5,2) DEFAULT 0      -- 合格率
feedback                TEXT                        -- 问题反馈
operator_id             INT NULL                    -- 操作员ID
completed_at            TIMESTAMP NULL              -- 完成时间
created_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

FOREIGN KEY (production_order_id) REFERENCES production_orders(id)
FOREIGN KEY (process_id) REFERENCES processes(id)
FOREIGN KEY (operator_id) REFERENCES users(id)
```

#### 6. 工序图片表（process_images）
```sql
id                          INT PRIMARY KEY AUTO_INCREMENT
production_order_process_id INT NOT NULL            -- 排产单工序ID
image_path                  VARCHAR(500) NOT NULL   -- 图片路径
image_name                  VARCHAR(200)            -- 图片名称
file_size                   INT                     -- 文件大小
created_at                  TIMESTAMP DEFAULT CURRENT_TIMESTAMP

FOREIGN KEY (production_order_process_id) REFERENCES production_order_processes(id)
```

## 技术实现要点

### 前端技术栈
- **Bootstrap 5**: 响应式UI框架
- **jQuery**: DOM操作和AJAX
- **Chart.js**: 数据可视化
- **Layui**: 弹窗和组件
- **Cropper.js**: 图片裁剪上传

### 后端技术栈
- **ThinkPHP 8.0**: 主框架
- **Think-ORM**: 数据库ORM
- **Think-Validate**: 数据验证
- **Think-Cache**: 缓存管理
- **Think-Queue**: 队列处理

### 关键功能实现

#### 1. 工序流转逻辑
```php
// 工序流转服务类
class ProcessFlowService
{
    public function transferToNext($orderId, $processId, $data)
    {
        // 1. 更新当前工序状态和质量数据
        // 2. 查找下一工序
        // 3. 更新排产单当前工序
        // 4. 如果是最后工序，完成排产单
    }
}
```

#### 2. 文件上传处理
```php
// 多图片上传处理
public function uploadImages()
{
    $files = request()->file('images');
    $uploadPath = 'uploads/process_images/';
    // 处理多文件上传逻辑
}
```

#### 3. 实时数据推送
- 使用WebSocket或Server-Sent Events
- 实时更新看板数据
- 移动端推送通知

## 系统特色功能

### 1. 智能工序流转
- 自动识别下一工序
- 工序状态实时更新
- 异常处理机制

### 2. 质量数据追溯
- 每道工序质量记录
- 问题反馈机制
- 照片证据保存

### 3. 实时数据看板
- 实时数据展示
- 图表动态更新
- 移动端适配

## 开发计划

### 第一阶段：基础框架搭建
- ThinkPHP 8.0项目初始化
- 数据库设计和创建
- 基础配置和环境搭建

### 第二阶段：用户管理和基础功能
- 用户认证系统
- 权限管理
- 基础CRUD功能

### 第三阶段：核心业务模块
- 工序管理模块
- 产品管理模块
- 排产管理模块

### 第四阶段：前端界面开发
- 管理端界面
- 工序操作员端界面
- 响应式适配

### 第五阶段：高级功能
- 大数据看板
- 实时数据推送
- 图片上传和处理

### 第六阶段：测试和部署
- 功能测试
- 性能测试
- 系统部署

## 非功能性需求

### 性能要求
- 页面响应时间 < 2秒
- 支持并发用户数 > 50
- 数据库查询优化

### 安全要求
- 用户认证和授权
- 数据传输加密
- SQL注入防护
- XSS攻击防护

### 可用性要求
- 7×24小时稳定运行
- 数据备份机制
- 错误日志记录

### 兼容性要求
- 支持主流浏览器
- 移动端适配
- 不同屏幕尺寸适配

---

**文档版本**: v1.0  
**创建日期**: 2025-07-08  
**最后更新**: 2025-07-08
