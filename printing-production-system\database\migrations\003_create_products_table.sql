-- 产品表
CREATE TABLE `pp_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `name` varchar(200) NOT NULL COMMENT '产品名称',
  `description` text COMMENT '产品描述',
  `abbreviation` varchar(20) NOT NULL COMMENT '产品缩写（用于快速索引）',
  `category` varchar(50) DEFAULT NULL COMMENT '产品分类',
  `specifications` varchar(200) DEFAULT NULL COMMENT '产品规格',
  `material` varchar(100) DEFAULT NULL COMMENT '材质',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色',
  `unit` varchar(20) DEFAULT '个' COMMENT '计量单位',
  `standard_price` decimal(10,2) DEFAULT NULL COMMENT '标准单价',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `image_path` varchar(255) DEFAULT NULL COMMENT '产品图片路径',
  `remark` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_abbreviation` (`abbreviation`),
  KEY `idx_name` (`name`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';

-- 插入示例产品数据
INSERT INTO `pp_products` (`name`, `description`, `abbreviation`, `category`, `specifications`, `material`, `color`, `unit`, `standard_price`, `status`) VALUES
('高档化妆品包装盒', '高端化妆品品牌专用包装盒', 'HZHZP001', '化妆品包装', '150x100x50mm', '350g铜版纸+哑膜', '粉色', '个', 8.50, 'active'),
('食品包装袋', '食品级安全包装袋', 'SPBZD001', '食品包装', '200x300mm', 'PE+PET复合膜', '透明', '个', 0.35, 'active'),
('电子产品包装盒', '数码产品专用包装盒', 'DZCP001', '电子包装', '300x200x100mm', '双瓦楞纸板', '白色', '个', 12.80, 'active'),
('药品包装盒', '医药行业专用包装盒', 'YPBZ001', '医药包装', '120x80x30mm', '300g白卡纸', '白色', '个', 2.20, 'active'),
('礼品包装袋', '节日礼品专用包装袋', 'LPBZD001', '礼品包装', '250x350x100mm', '157g铜版纸', '红色', '个', 3.60, 'active');
