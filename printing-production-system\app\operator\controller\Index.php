<?php
declare (strict_types = 1);

namespace app\operator\controller;

/**
 * 工序操作员端首页控制器
 */
class Index extends BaseController
{
    /**
     * 首页
     * @return string
     */
    public function index()
    {
        $user = get_operator_user();
        return $this->renderDashboard($user);
    }

    /**
     * 渲染操作员端仪表板
     * @param array $user
     * @return string
     */
    private function renderDashboard($user)
    {
        return '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作员端首页 - 包装印刷排产管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); min-height: 100vh; }
        .sidebar .nav-link { color: rgba(255,255,255,0.8); }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: white; background: rgba(255,255,255,0.1); }
        .main-content { background: #f8f9fa; min-height: 100vh; }
        .task-card { border-left: 4px solid #74b9ff; }
        .task-card.urgent { border-left-color: #e17055; }
        .task-card.normal { border-left-color: #00b894; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-0">
                <div class="p-3 text-white">
                    <h5><i class="bi bi-person-workspace"></i> 操作员端</h5>
                    <small>欢迎，' . htmlspecialchars($user['real_name'] ?: $user['username']) . '</small>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link active" href="/operator/index">
                        <i class="bi bi-house"></i> 首页
                    </a>
                    <a class="nav-link" href="/operator/production">
                        <i class="bi bi-list-task"></i> 我的任务
                    </a>
                    <a class="nav-link" href="/operator/process">
                        <i class="bi bi-gear"></i> 工序操作
                    </a>
                    <hr class="text-white-50">
                    <a class="nav-link" href="javascript:void(0)" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> 退出登录
                    </a>
                </nav>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>工作台</h2>
                    <span class="text-muted">' . date('Y-m-d H:i:s') . '</span>
                </div>

                <!-- 任务概览 -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card task-card">
                            <div class="card-body text-center">
                                <i class="bi bi-clock-history fs-1 text-primary"></i>
                                <h5 class="mt-2">待处理任务</h5>
                                <h3 class="text-primary">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card task-card normal">
                            <div class="card-body text-center">
                                <i class="bi bi-play-circle fs-1 text-success"></i>
                                <h5 class="mt-2">进行中任务</h5>
                                <h3 class="text-success">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card task-card urgent">
                            <div class="card-body text-center">
                                <i class="bi bi-exclamation-triangle fs-1 text-danger"></i>
                                <h5 class="mt-2">紧急任务</h5>
                                <h3 class="text-danger">0</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 今日任务 -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-calendar-day"></i> 今日任务</h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-inbox fs-1"></i>
                                    <p class="mt-2">暂无任务</p>
                                    <small>当管理员分配工序任务后，将在这里显示</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-person-circle"></i> 个人信息</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>姓名：</strong> ' . htmlspecialchars($user['real_name'] ?: $user['username']) . '</p>
                                <p><strong>用户名：</strong> ' . htmlspecialchars($user['username']) . '</p>
                                <p><strong>角色：</strong> 工序操作员</p>
                                <p><strong>登录时间：</strong> ' . date('Y-m-d H:i:s', $user['login_time']) . '</p>
                                <hr>
                                <div class="d-grid">
                                    <button class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-gear"></i> 个人设置
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5><i class="bi bi-info-circle"></i> 操作提示</h5>
                            </div>
                            <div class="card-body">
                                <small class="text-muted">
                                    <p>• 点击"我的任务"查看分配给您的工序任务</p>
                                    <p>• 完成工序后记得填写质量数据</p>
                                    <p>• 上传工序照片作为质量证据</p>
                                    <p>• 及时反馈工序中遇到的问题</p>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function logout() {
            if (confirm("确定要退出登录吗？")) {
                fetch("/operator/logout", {
                    method: "POST"
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        window.location.href = data.data.redirect;
                    } else {
                        alert(data.msg);
                    }
                })
                .catch(error => {
                    alert("退出失败：" + error.message);
                });
            }
        }
    </script>
</body>
</html>';
    }
}
