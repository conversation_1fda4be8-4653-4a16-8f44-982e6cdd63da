<?php
declare (strict_types = 1);

namespace app\operator\controller;

/**
 * 工序操作员端首页控制器
 */
class Index extends BaseController
{
    /**
     * 首页
     * @return string
     */
    public function index()
    {
        return '<h1>包装印刷公司排产管理系统 - 工序操作员端</h1>
                <p>ThinkPHP 8.0 框架搭建成功！</p>
                <p>当前时间：' . date('Y-m-d H:i:s') . '</p>
                <hr>
                <h3>功能模块：</h3>
                <ul>
                    <li>排产单查看</li>
                    <li>工序操作</li>
                    <li>质量数据录入</li>
                    <li>工序流转</li>
                </ul>';
    }
}
