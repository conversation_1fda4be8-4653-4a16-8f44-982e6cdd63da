# 包装印刷公司排产管理系统 - 项目结构说明

## 项目概述

**项目名称**: 包装印刷公司排产管理系统  
**技术栈**: ThinkPHP 8.0 + H5 + Bootstrap 5  
**项目状态**: 第一阶段基础框架搭建完成  
**访问地址**: 
- 管理端: http://localhost:8000/admin
- 工序操作员端: http://localhost:8000/operator

## 项目目录结构

```
printing-production-system/
├── app/                          # 应用目录
│   ├── admin/                    # 管理端应用
│   │   ├── controller/           # 控制器
│   │   │   ├── BaseController.php    # 基础控制器
│   │   │   └── Index.php            # 首页控制器
│   │   ├── middleware/           # 中间件
│   │   │   └── AdminAuth.php        # 管理员认证中间件
│   │   ├── model/               # 模型
│   │   ├── view/                # 视图
│   │   ├── common.php           # 公共函数
│   │   └── route.php            # 路由配置
│   │
│   ├── operator/                # 工序操作员端应用
│   │   ├── controller/          # 控制器
│   │   │   ├── BaseController.php   # 基础控制器
│   │   │   └── Index.php           # 首页控制器
│   │   ├── middleware/          # 中间件
│   │   │   └── OperatorAuth.php     # 操作员认证中间件
│   │   ├── model/              # 模型
│   │   ├── view/               # 视图
│   │   ├── common.php          # 公共函数
│   │   └── route.php           # 路由配置
│   │
│   ├── common/                 # 公共模块
│   │   ├── model/              # 公共模型
│   │   │   └── BaseModel.php       # 基础模型类
│   │   ├── service/            # 服务层
│   │   └── validate/           # 验证器
│   │
│   └── controller/             # 默认控制器目录
│
├── config/                     # 配置文件
│   ├── app.php                 # 应用配置
│   ├── database.php            # 数据库配置
│   └── ...                     # 其他配置文件
│
├── public/                     # 公共资源目录
│   ├── static/                 # 静态资源
│   │   ├── admin/              # 管理端静态资源
│   │   └── operator/           # 操作员端静态资源
│   ├── uploads/                # 上传文件目录
│   │   └── process_images/     # 工序图片目录
│   └── index.php               # 入口文件
│
├── database/                   # 数据库相关
│   ├── migrations/             # 数据库迁移文件
│   └── seeds/                  # 数据填充文件
│
├── vendor/                     # Composer依赖包
├── .env                        # 环境配置文件
├── composer.json               # Composer配置
└── 项目结构说明.md             # 本文件
```

## 已完成功能

### 1. 基础框架搭建
- ✅ ThinkPHP 8.0 项目初始化
- ✅ 多应用模式配置（admin + operator）
- ✅ 基础目录结构创建
- ✅ 环境配置文件设置

### 2. 应用架构设计
- ✅ 管理端应用结构
- ✅ 工序操作员端应用结构
- ✅ 公共模块设计
- ✅ 基础控制器类
- ✅ 基础模型类

### 3. 路由配置
- ✅ 管理端路由规划
- ✅ 操作员端路由规划
- ✅ 多应用路由分离

### 4. 认证中间件
- ✅ 管理员认证中间件
- ✅ 操作员认证中间件
- ✅ 权限控制基础框架

### 5. 测试验证
- ✅ 服务器启动测试
- ✅ 管理端访问测试
- ✅ 操作员端访问测试

## 环境配置

### 数据库配置 (.env)
```
DB_TYPE = mysql
DB_HOST = 127.0.0.1
DB_NAME = printing_production
DB_USER = root
DB_PASS = 
DB_PORT = 3306
DB_CHARSET = utf8mb4
DB_PREFIX = pp_
```

### 应用配置
- 调试模式: 开启
- 多应用模式: 启用
- 时区: Asia/Shanghai
- 默认语言: zh-cn

## 技术特点

### 1. 双端架构
- **管理端**: 完整的后台管理功能
- **操作员端**: 专门的工序操作界面
- **独立路由**: 各端独立的路由配置

### 2. 模块化设计
- **控制器分层**: 基础控制器 + 业务控制器
- **模型分层**: 基础模型 + 业务模型
- **服务层**: 业务逻辑封装
- **验证器**: 数据验证统一管理

### 3. 安全机制
- **认证中间件**: 登录状态验证
- **权限控制**: 基于角色的权限管理
- **数据验证**: 输入数据安全验证

## 下一步开发计划

### 第二阶段：数据库设计
- [ ] 创建数据库表结构
- [ ] 设计表关系和索引
- [ ] 创建数据迁移文件
- [ ] 准备测试数据

### 第三阶段：用户管理模块
- [ ] 用户认证系统
- [ ] 用户CRUD功能
- [ ] 权限管理
- [ ] 登录/登出功能

### 第四阶段：核心业务模块
- [ ] 工序管理
- [ ] 产品管理
- [ ] 排产管理
- [ ] 工序执行

### 第五阶段：前端界面
- [ ] Bootstrap 5 界面设计
- [ ] 响应式布局
- [ ] 交互功能实现
- [ ] 大数据看板

## 启动说明

### 开发环境启动
```bash
# 进入项目目录
cd printing-production-system

# 启动内置服务器
php think run

# 访问地址
# 管理端: http://localhost:8000/admin
# 操作员端: http://localhost:8000/operator
```

### 依赖安装
```bash
# 安装Composer依赖
composer install

# 安装多应用扩展
composer require topthink/think-multi-app
```

## 注意事项

1. **环境要求**: PHP 8.0+, MySQL 8.0+
2. **权限设置**: 确保 runtime 和 public/uploads 目录可写
3. **数据库**: 需要手动创建数据库 `printing_production`
4. **调试模式**: 生产环境需要关闭调试模式

---

**文档版本**: v1.0  
**更新时间**: 2025-07-08  
**状态**: 第一阶段完成
