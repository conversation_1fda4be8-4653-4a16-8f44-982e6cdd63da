-- 排产单表
CREATE TABLE `pp_production_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '排产单ID',
  `order_no` varchar(50) NOT NULL COMMENT '排产单号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `quantity` int(11) NOT NULL COMMENT '排产数量',
  `plan_date` date NOT NULL COMMENT '计划开始时间',
  `delivery_date` date NOT NULL COMMENT '交货时间',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序（数字越小优先级越高）',
  `status` enum('pending','running','paused','completed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态：pending-未开始，running-进行中，paused-暂停，completed-完成，cancelled-取消',
  `current_process_id` int(11) DEFAULT NULL COMMENT '当前工序ID',
  `progress` decimal(5,2) DEFAULT 0.00 COMMENT '完成进度（百分比）',
  `total_qualified_quantity` int(11) DEFAULT 0 COMMENT '总合格数量',
  `total_defective_quantity` int(11) DEFAULT 0 COMMENT '总不合格数量',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `customer_contact` varchar(100) DEFAULT NULL COMMENT '客户联系方式',
  `urgent_level` tinyint(4) DEFAULT 1 COMMENT '紧急程度（1-5，5最紧急）',
  `remark` text COMMENT '备注',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '实际开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '实际完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_current_process_id` (`current_process_id`),
  KEY `idx_status` (`status`),
  KEY `idx_plan_date` (`plan_date`),
  KEY `idx_delivery_date` (`delivery_date`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_urgent_level` (`urgent_level`),
  CONSTRAINT `fk_production_orders_product` FOREIGN KEY (`product_id`) REFERENCES `pp_products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_production_orders_process` FOREIGN KEY (`current_process_id`) REFERENCES `pp_processes` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排产单表';
