<?php
/**
 * 调试登录问题
 */

require_once 'vendor/autoload.php';

$app = new \think\App();
$app->initialize();

echo "===========================================\n";
echo "登录调试\n";
echo "===========================================\n\n";

try {
    // 模拟POST请求
    $_POST['username'] = 'admin';
    $_POST['password'] = 'password';
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
    
    // 创建登录控制器实例
    $login = new \app\admin\controller\Login($app);
    
    echo "1. 测试登录方法调用\n";
    echo "-------------------------------------------\n";
    
    $result = $login->login();
    
    if ($result instanceof \think\response\Json) {
        echo "返回类型: JSON响应\n";
        echo "响应内容: " . $result->getContent() . "\n";
    } else {
        echo "返回类型: " . gettype($result) . "\n";
        echo "响应内容: " . substr($result, 0, 200) . "...\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n===========================================\n";
echo "调试完成\n";
echo "===========================================\n";
