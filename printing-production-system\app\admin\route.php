<?php
// 管理端路由配置

use think\facade\Route;

// 登录相关路由
Route::group('', function () {
    Route::get('login', 'Login/index');
    Route::post('login', 'Login/login');
    Route::post('logout', 'Login/logout');
});

// 需要登录的路由组
Route::group('', function () {
    // 首页
    Route::get('/', 'Index/index');
    Route::get('index', 'Index/index');
    
    // 用户管理
    Route::group('user', function () {
        Route::get('/', 'User/index');
        Route::get('create', 'User/create');
        Route::post('save', 'User/save');
        Route::get('edit/:id', 'User/edit');
        Route::post('update/:id', 'User/update');
        Route::delete('delete/:id', 'User/delete');
        Route::post('status/:id', 'User/status');
    });
    
    // 工序管理
    Route::group('process', function () {
        Route::get('/', 'Process/index');
        Route::get('create', 'Process/create');
        Route::post('save', 'Process/save');
        Route::get('edit/:id', 'Process/edit');
        Route::post('update/:id', 'Process/update');
        Route::delete('delete/:id', 'Process/delete');
        Route::post('status/:id', 'Process/status');
        Route::post('sort', 'Process/sort');
    });
    
    // 产品管理
    Route::group('product', function () {
        Route::get('/', 'Product/index');
        Route::get('create', 'Product/create');
        Route::post('save', 'Product/save');
        Route::get('edit/:id', 'Product/edit');
        Route::post('update/:id', 'Product/update');
        Route::delete('delete/:id', 'Product/delete');
        Route::post('status/:id', 'Product/status');
        Route::get('search', 'Product/search');
    });
    
    // 排产管理
    Route::group('production', function () {
        Route::get('/', 'Production/index');
        Route::get('create', 'Production/create');
        Route::post('save', 'Production/save');
        Route::get('edit/:id', 'Production/edit');
        Route::post('update/:id', 'Production/update');
        Route::delete('delete/:id', 'Production/delete');
        Route::post('status/:id', 'Production/status');
        Route::get('processes/:id', 'Production/processes');
        Route::post('processes/:id', 'Production/saveProcesses');
    });
    
    // 大数据看板
    Route::group('dashboard', function () {
        Route::get('/', 'Dashboard/index');
        Route::get('data', 'Dashboard/data');
        Route::get('statistics', 'Dashboard/statistics');
    });
    
    // 文件上传
    Route::post('upload', 'Upload/index');
    
});

return [];
