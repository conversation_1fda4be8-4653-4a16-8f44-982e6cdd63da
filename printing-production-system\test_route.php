<?php
/**
 * 测试路由配置
 */

require_once 'vendor/autoload.php';

$app = new \think\App();
$app->initialize();

echo "===========================================\n";
echo "路由测试\n";
echo "===========================================\n\n";

try {
    // 模拟请求环境
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/admin/login';
    $_SERVER['HTTP_HOST'] = 'localhost:8000';
    $_POST['username'] = 'admin';
    $_POST['password'] = 'password';
    
    echo "1. 当前请求信息\n";
    echo "-------------------------------------------\n";
    echo "请求方法: " . $_SERVER['REQUEST_METHOD'] . "\n";
    echo "请求URI: " . $_SERVER['REQUEST_URI'] . "\n";
    echo "POST数据: " . json_encode($_POST) . "\n\n";
    
    echo "2. 测试路由解析\n";
    echo "-------------------------------------------\n";
    
    // 获取当前应用
    $request = $app->request;
    echo "应用名: " . $app->http->getName() . "\n";
    echo "控制器: " . $request->controller() . "\n";
    echo "方法: " . $request->action() . "\n";
    echo "是否POST: " . ($request->isPost() ? 'Yes' : 'No') . "\n\n";
    
    echo "3. 测试控制器调用\n";
    echo "-------------------------------------------\n";
    
    // 直接调用HTTP应用
    $response = $app->http->run();
    
    echo "响应类型: " . get_class($response) . "\n";
    echo "响应内容: " . substr($response->getContent(), 0, 200) . "...\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n===========================================\n";
echo "路由测试完成\n";
echo "===========================================\n";
