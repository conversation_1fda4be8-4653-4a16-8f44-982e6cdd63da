<?php
declare (strict_types = 1);

namespace app\common\model;

/**
 * 用户模型
 */
class User extends BaseModel
{
    // 表名
    protected $name = 'users';
    
    // 角色常量
    const ROLE_ADMIN = 'admin';
    const ROLE_OPERATOR = 'operator';
    
    // 角色文本映射
    protected $roleText = [
        self::ROLE_ADMIN => '管理员',
        self::ROLE_OPERATOR => '操作员',
    ];
    
    // 隐藏字段
    protected $hidden = ['password', 'deleted_at'];
    
    // 类型转换
    protected $type = [
        'last_login_time' => 'timestamp',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp',
        'deleted_at' => 'timestamp',
    ];
    
    /**
     * 获取角色文本
     * @param string $role
     * @return string
     */
    public function getRoleText($role = '')
    {
        $role = $role ?: $this->role;
        return $this->roleText[$role] ?? '未知';
    }
    
    /**
     * 角色获取器
     * @param $value
     * @return string
     */
    public function getRoleAttr($value)
    {
        return $value;
    }
    
    /**
     * 密码修改器
     * @param $value
     * @return string
     */
    public function setPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }
    
    /**
     * 验证密码
     * @param string $password 明文密码
     * @param string $hash 加密后的密码
     * @return bool
     */
    public static function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * 根据用户名获取用户
     * @param string $username
     * @return User|null
     */
    public static function getByUsername($username)
    {
        return self::where('username', $username)
                   ->where('deleted_at', null)
                   ->find();
    }
    
    /**
     * 获取管理员列表
     * @return \think\Collection
     */
    public static function getAdmins()
    {
        return self::where('role', self::ROLE_ADMIN)
                   ->where('status', self::STATUS_ACTIVE)
                   ->where('deleted_at', null)
                   ->select();
    }
    
    /**
     * 获取操作员列表
     * @return \think\Collection
     */
    public static function getOperators()
    {
        return self::where('role', self::ROLE_OPERATOR)
                   ->where('status', self::STATUS_ACTIVE)
                   ->where('deleted_at', null)
                   ->select();
    }
    
    /**
     * 更新最后登录信息
     * @param string $ip
     * @return bool
     */
    public function updateLastLogin($ip = '')
    {
        return $this->save([
            'last_login_time' => time(),
            'last_login_ip' => $ip ?: request()->ip(),
        ]);
    }
    
    /**
     * 检查用户是否有工序权限
     * @param int $processId
     * @param string $permissionType
     * @return bool
     */
    public function hasProcessPermission($processId, $permissionType = 'execute')
    {
        if ($this->role === self::ROLE_ADMIN) {
            return true; // 管理员拥有所有权限
        }
        
        return UserProcessPermission::where('user_id', $this->id)
                                   ->where('process_id', $processId)
                                   ->where('permission_type', $permissionType)
                                   ->count() > 0;
    }
}
