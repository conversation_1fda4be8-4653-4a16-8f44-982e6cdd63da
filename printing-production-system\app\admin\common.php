<?php
// 管理端公共函数文件

/**
 * 检查管理员权限
 * @param string $permission 权限标识
 * @return bool
 */
function check_admin_permission($permission = '')
{
    // 这里可以实现具体的权限检查逻辑
    return true;
}

/**
 * 获取当前登录管理员信息
 * @return array|null
 */
function get_admin_user()
{
    return session('admin_user');
}

/**
 * 管理员登录检查
 * @return bool
 */
function is_admin_login()
{
    return !empty(session('admin_user'));
}

/**
 * 格式化文件大小
 * @param int $size 文件大小（字节）
 * @return string
 */
function format_file_size($size)
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $index = 0;
    
    while ($size >= 1024 && $index < count($units) - 1) {
        $size /= 1024;
        $index++;
    }
    
    return round($size, 2) . ' ' . $units[$index];
}
