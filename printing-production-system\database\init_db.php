<?php
/**
 * 数据库初始化脚本
 * 使用方法: php database/init_db.php
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../vendor/autoload.php';

// 初始化应用
$app = new \think\App();
$app->initialize();

// 引入数据库初始化类
require_once __DIR__ . '/DatabaseSeeder.php';

echo "===========================================\n";
echo "包装印刷公司排产管理系统 - 数据库初始化工具\n";
echo "===========================================\n\n";

// 检查数据库连接
echo "1. 检查数据库连接...\n";
if (!DatabaseSeeder::checkConnection()) {
    echo "请检查数据库配置后重试。\n";
    exit(1);
}

echo "\n2. 检查现有表结构...\n";
$tablesExist = DatabaseSeeder::checkTables();

if ($tablesExist) {
    echo "\n警告: 数据库表已存在！\n";
    echo "继续执行将会删除现有数据并重新创建表结构。\n";
    echo "是否继续? (y/N): ";
    
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);
    
    if (trim(strtolower($line)) !== 'y') {
        echo "操作已取消。\n";
        exit(0);
    }
}

echo "\n3. 开始执行数据库初始化...\n";
echo "-------------------------------------------\n";

$startTime = microtime(true);
$success = DatabaseSeeder::run();
$endTime = microtime(true);

echo "-------------------------------------------\n";

if ($success) {
    echo "✓ 数据库初始化完成！\n";
    echo "耗时: " . round($endTime - $startTime, 2) . " 秒\n\n";
    
    echo "初始账户信息:\n";
    echo "管理员账户: admin / password\n";
    echo "操作员账户: operator1 / password\n";
    echo "操作员账户: operator2 / password\n";
    echo "操作员账户: operator3 / password\n\n";
    
    echo "数据库表结构:\n";
    echo "- pp_users (用户表)\n";
    echo "- pp_processes (工序表)\n";
    echo "- pp_products (产品表)\n";
    echo "- pp_production_orders (排产单表)\n";
    echo "- pp_production_order_processes (排产单工序表)\n";
    echo "- pp_process_images (工序图片表)\n";
    echo "- pp_user_process_permissions (用户工序权限表)\n";
    echo "- pp_operation_logs (操作日志表)\n\n";
    
    echo "示例数据:\n";
    echo "- 7个标准工序流程\n";
    echo "- 7种产品类型\n";
    echo "- 3个示例排产单\n";
    echo "- 完整的工序流程配置\n\n";
    
    echo "现在可以访问系统:\n";
    echo "管理端: http://localhost:8000/admin\n";
    echo "操作员端: http://localhost:8000/operator\n";
    
} else {
    echo "✗ 数据库初始化失败！\n";
    echo "请检查错误信息并重试。\n";
    exit(1);
}

echo "\n===========================================\n";
echo "数据库初始化完成\n";
echo "===========================================\n";
