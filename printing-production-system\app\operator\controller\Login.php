<?php
declare (strict_types = 1);

namespace app\operator\controller;

use app\common\model\User;
use app\common\model\OperationLog;
use think\App;

/**
 * 操作员端登录控制器
 */
class Login
{
    protected $request;
    protected $app;

    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;
    }

    /**
     * 登录页面
     * @return string
     */
    public function index()
    {
        // 如果已经登录，跳转到首页
        if (is_operator_login()) {
            return redirect('/operator/index');
        }

        return $this->renderLoginPage();
    }

    /**
     * 登录处理
     * @return \think\response\Json
     */
    public function login()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        $username = $this->request->post('username', '');
        $password = $this->request->post('password', '');

        // 验证输入
        if (empty($username) || empty($password)) {
            return json(['code' => 400, 'msg' => '用户名和密码不能为空']);
        }

        try {
            // 查找用户
            $user = User::getByUsername($username);
            if (!$user) {
                return json(['code' => 401, 'msg' => '用户名或密码错误']);
            }

            // 检查用户状态
            if ($user->status !== User::STATUS_ACTIVE) {
                return json(['code' => 403, 'msg' => '账户已被禁用，请联系管理员']);
            }

            // 检查用户角色
            if ($user->role !== User::ROLE_OPERATOR) {
                return json(['code' => 403, 'msg' => '无权限访问操作员端']);
            }

            // 验证密码
            if (!User::verifyPassword($password, $user->password)) {
                return json(['code' => 401, 'msg' => '用户名或密码错误']);
            }

            // 登录成功，设置会话
            $userData = [
                'id' => $user->id,
                'username' => $user->username,
                'real_name' => $user->real_name,
                'role' => $user->role,
                'avatar' => $user->avatar,
                'login_time' => time(),
            ];

            session('operator_user', $userData);

            // 更新最后登录信息
            $user->updateLastLogin($this->request->ip());

            // 记录登录日志
            OperationLog::log('user', 'login', '操作员登录', null, $userData);

            return json([
                'code' => 200,
                'msg' => '登录成功',
                'data' => [
                    'redirect' => '/operator/index'
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '登录失败：' . $e->getMessage()]);
        }
    }

    /**
     * 登出处理
     * @return \think\response\Json
     */
    public function logout()
    {
        $user = session('operator_user');
        
        // 清除会话
        session('operator_user', null);

        // 记录登出日志
        if ($user) {
            OperationLog::log('user', 'logout', '操作员登出', $user);
        }

        if ($this->request->isAjax()) {
            return json([
                'code' => 200,
                'msg' => '登出成功',
                'data' => [
                    'redirect' => '/operator/login'
                ]
            ]);
        }

        return redirect('/operator/login');
    }

    /**
     * 渲染登录页面
     * @return string
     */
    private function renderLoginPage()
    {
        return '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作员登录 - 包装印刷排产管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); min-height: 100vh; }
        .login-container { min-height: 100vh; display: flex; align-items: center; }
        .login-card { background: rgba(255,255,255,0.95); border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
        .login-header { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; border-radius: 15px 15px 0 0; }
        .btn-login { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); border: none; }
        .btn-login:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container-fluid login-container">
        <div class="row justify-content-center w-100">
            <div class="col-md-4">
                <div class="card login-card">
                    <div class="card-header login-header text-center py-4">
                        <h3><i class="bi bi-person-workspace"></i> 操作员登录</h3>
                        <p class="mb-0">包装印刷排产管理系统</p>
                    </div>
                    <div class="card-body p-5">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-person"></i> 用户名</label>
                                <input type="text" class="form-control" name="username" placeholder="请输入用户名" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-lock"></i> 密码</label>
                                <input type="password" class="form-control" name="password" placeholder="请输入密码" required>
                            </div>
                            <button type="submit" class="btn btn-login btn-primary w-100 py-2">
                                <i class="bi bi-box-arrow-in-right"></i> 登录
                            </button>
                        </form>
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                示例账户：operator1 / password<br>
                                <a href="/admin/login" class="text-decoration-none">管理员登录</a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById("loginForm").addEventListener("submit", function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector("button[type=submit]");
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = "<i class=\"bi bi-hourglass-split\"></i> 登录中...";
            submitBtn.disabled = true;
            
            fetch("/operator/login", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert("登录成功！");
                    window.location.href = data.data.redirect;
                } else {
                    alert(data.msg);
                }
            })
            .catch(error => {
                alert("登录失败：" + error.message);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>';
    }
}
