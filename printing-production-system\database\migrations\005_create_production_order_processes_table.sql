-- 排产单工序表
CREATE TABLE `pp_production_order_processes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `production_order_id` int(11) NOT NULL COMMENT '排产单ID',
  `process_id` int(11) NOT NULL COMMENT '工序ID',
  `sort_order` int(11) NOT NULL COMMENT '工序排序（在该排产单中的顺序）',
  `status` enum('pending','running','completed','skipped') NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待处理，running-进行中，completed-已完成，skipped-跳过',
  `qualified_quantity` int(11) DEFAULT 0 COMMENT '合格数量',
  `defective_quantity` int(11) DEFAULT 0 COMMENT '不合格数量',
  `qualification_rate` decimal(5,2) DEFAULT 0.00 COMMENT '合格率（百分比）',
  `feedback` text COMMENT '问题反馈',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作员ID',
  `estimated_time` int(11) DEFAULT NULL COMMENT '预估耗时（分钟）',
  `actual_time` int(11) DEFAULT NULL COMMENT '实际耗时（分钟）',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_process` (`production_order_id`, `process_id`),
  KEY `idx_production_order_id` (`production_order_id`),
  KEY `idx_process_id` (`process_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_order_processes_order` FOREIGN KEY (`production_order_id`) REFERENCES `pp_production_orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_order_processes_process` FOREIGN KEY (`process_id`) REFERENCES `pp_processes` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_order_processes_operator` FOREIGN KEY (`operator_id`) REFERENCES `pp_users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排产单工序表';
