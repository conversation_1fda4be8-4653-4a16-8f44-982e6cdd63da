[2025-07-08T19:01:57+08:00][sql] CONNECT:[ UseTime:0.057008s ] mysql:host=127.0.0.1;port=3306;dbname=prodsys;charset=utf8mb4
[2025-07-08T19:01:57+08:00][sql] SELECT 1 [ RunTime:0.000957s ]
[2025-07-08T19:01:57+08:00][sql] SHOW TABLES LIKE 'pp_users' [ RunTime:0.000298s ]
[2025-07-08T19:01:57+08:00][sql] SHOW TABLES LIKE 'pp_processes' [ RunTime:0.000224s ]
[2025-07-08T19:01:57+08:00][sql] SHOW TABLES LIKE 'pp_products' [ RunTime:0.000234s ]
[2025-07-08T19:01:57+08:00][sql] SHOW TABLES LIKE 'pp_production_orders' [ RunTime:0.000227s ]
[2025-07-08T19:01:57+08:00][sql] SHOW TABLES LIKE 'pp_production_order_processes' [ RunTime:0.000233s ]
[2025-07-08T19:01:57+08:00][sql] SHOW TABLES LIKE 'pp_process_images' [ RunTime:0.000218s ]
[2025-07-08T19:01:57+08:00][sql] SHOW TABLES LIKE 'pp_user_process_permissions' [ RunTime:0.000227s ]
[2025-07-08T19:01:57+08:00][sql] SHOW TABLES LIKE 'pp_operation_logs' [ RunTime:0.000188s ]
[2025-07-08T19:01:57+08:00][sql] SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO" [ RunTime:0.000133s ]
[2025-07-08T19:01:57+08:00][sql] SET AUTOCOMMIT = 0 [ RunTime:0.000143s ]
[2025-07-08T19:01:57+08:00][sql] START TRANSACTION [ RunTime:0.000132s ]
[2025-07-08T19:01:57+08:00][sql] SET time_zone = "+00:00" [ RunTime:0.000111s ]
[2025-07-08T19:01:57+08:00][sql] DROP TABLE IF EXISTS `pp_users` [ RunTime:0.000244s ]
[2025-07-08T19:01:57+08:00][sql] CREATE TABLE `pp_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（加密）',
  `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `role` enum('admin','operator') NOT NULL DEFAULT 'operator' COMMENT '角色：admin-管理员，operator-操作员',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像路径',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表' [ RunTime:0.007707s ]
[2025-07-08T19:01:57+08:00][sql] DROP TABLE IF EXISTS `pp_processes` [ RunTime:0.000221s ]
[2025-07-08T19:01:57+08:00][sql] CREATE TABLE `pp_processes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '工序ID',
  `name` varchar(100) NOT NULL COMMENT '工序名称',
  `description` text COMMENT '工序描述',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `estimated_time` int(11) DEFAULT NULL COMMENT '预估耗时（分钟）',
  `difficulty_level` tinyint(4) DEFAULT 1 COMMENT '难度等级（1-5）',
  `quality_standard` text COMMENT '质量标准说明',
  `operation_guide` text COMMENT '操作指南',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工序表' [ RunTime:0.007245s ]
[2025-07-08T19:01:57+08:00][sql] DROP TABLE IF EXISTS `pp_products` [ RunTime:0.000256s ]
[2025-07-08T19:01:57+08:00][sql] CREATE TABLE `pp_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `name` varchar(200) NOT NULL COMMENT '产品名称',
  `description` text COMMENT '产品描述',
  `abbreviation` varchar(20) NOT NULL COMMENT '产品缩写（用于快速索引）',
  `category` varchar(50) DEFAULT NULL COMMENT '产品分类',
  `specifications` varchar(200) DEFAULT NULL COMMENT '产品规格',
  `material` varchar(100) DEFAULT NULL COMMENT '材质',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色',
  `unit` varchar(20) DEFAULT '个' COMMENT '计量单位',
  `standard_price` decimal(10,2) DEFAULT NULL COMMENT '标准单价',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `image_path` varchar(255) DEFAULT NULL COMMENT '产品图片路径',
  `remark` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_abbreviation` (`abbreviation`),
  KEY `idx_name` (`name`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表' [ RunTime:0.006366s ]
[2025-07-08T19:01:57+08:00][sql] DROP TABLE IF EXISTS `pp_production_orders` [ RunTime:0.000223s ]
[2025-07-08T19:01:57+08:00][sql] CREATE TABLE `pp_production_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '排产单ID',
  `order_no` varchar(50) NOT NULL COMMENT '排产单号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `quantity` int(11) NOT NULL COMMENT '排产数量',
  `plan_date` date NOT NULL COMMENT '计划开始时间',
  `delivery_date` date NOT NULL COMMENT '交货时间',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序（数字越小优先级越高）',
  `status` enum('pending','running','paused','completed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态：pending-未开始，running-进行中，paused-暂停，completed-完成，cancelled-取消',
  `current_process_id` int(11) DEFAULT NULL COMMENT '当前工序ID',
  `progress` decimal(5,2) DEFAULT 0.00 COMMENT '完成进度（百分比）',
  `total_qualified_quantity` int(11) DEFAULT 0 COMMENT '总合格数量',
  `total_defective_quantity` int(11) DEFAULT 0 COMMENT '总不合格数量',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `customer_contact` varchar(100) DEFAULT NULL COMMENT '客户联系方式',
  `urgent_level` tinyint(4) DEFAULT 1 COMMENT '紧急程度（1-5，5最紧急）',
  `remark` text COMMENT '备注',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '实际开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '实际完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_current_process_id` (`current_process_id`),
  KEY `idx_status` (`status`),
  KEY `idx_plan_date` (`plan_date`),
  KEY `idx_delivery_date` (`delivery_date`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_urgent_level` (`urgent_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排产单表' [ RunTime:0.008128s ]
[2025-07-08T19:01:57+08:00][sql] DROP TABLE IF EXISTS `pp_production_order_processes` [ RunTime:0.000263s ]
[2025-07-08T19:01:57+08:00][sql] CREATE TABLE `pp_production_order_processes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `production_order_id` int(11) NOT NULL COMMENT '排产单ID',
  `process_id` int(11) NOT NULL COMMENT '工序ID',
  `sort_order` int(11) NOT NULL COMMENT '工序排序（在该排产单中的顺序）',
  `status` enum('pending','running','completed','skipped') NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待处理，running-进行中，completed-已完成，skipped-跳过',
  `qualified_quantity` int(11) DEFAULT 0 COMMENT '合格数量',
  `defective_quantity` int(11) DEFAULT 0 COMMENT '不合格数量',
  `qualification_rate` decimal(5,2) DEFAULT 0.00 COMMENT '合格率（百分比）',
  `feedback` text COMMENT '问题反馈',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作员ID',
  `estimated_time` int(11) DEFAULT NULL COMMENT '预估耗时（分钟）',
  `actual_time` int(11) DEFAULT NULL COMMENT '实际耗时（分钟）',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_process` (`production_order_id`, `process_id`),
  KEY `idx_production_order_id` (`production_order_id`),
  KEY `idx_process_id` (`process_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排产单工序表' [ RunTime:0.006833s ]
[2025-07-08T19:01:57+08:00][sql] DROP TABLE IF EXISTS `pp_process_images` [ RunTime:0.000240s ]
[2025-07-08T19:01:57+08:00][sql] CREATE TABLE `pp_process_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `production_order_process_id` int(11) NOT NULL COMMENT '排产单工序ID',
  `image_path` varchar(500) NOT NULL COMMENT '图片路径',
  `image_name` varchar(200) DEFAULT NULL COMMENT '图片名称',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小（字节）',
  `image_type` varchar(20) DEFAULT NULL COMMENT '图片类型（jpg, png, gif等）',
  `width` int(11) DEFAULT NULL COMMENT '图片宽度',
  `height` int(11) DEFAULT NULL COMMENT '图片高度',
  `description` varchar(500) DEFAULT NULL COMMENT '图片描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_production_order_process_id` (`production_order_process_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工序图片表' [ RunTime:0.005964s ]
[2025-07-08T19:01:57+08:00][sql] DROP TABLE IF EXISTS `pp_user_process_permissions` [ RunTime:0.000290s ]
[2025-07-08T19:01:57+08:00][sql] CREATE TABLE `pp_user_process_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `process_id` int(11) NOT NULL COMMENT '工序ID',
  `permission_type` enum('read','write','execute') NOT NULL DEFAULT 'execute' COMMENT '权限类型：read-查看，write-编辑，execute-执行',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_process_permission` (`user_id`, `process_id`, `permission_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_process_id` (`process_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户工序权限表' [ RunTime:0.006050s ]
[2025-07-08T19:01:57+08:00][sql] DROP TABLE IF EXISTS `pp_operation_logs` [ RunTime:0.000262s ]
[2025-07-08T19:01:57+08:00][sql] CREATE TABLE `pp_operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) DEFAULT NULL COMMENT '操作用户ID',
  `module` varchar(50) NOT NULL COMMENT '操作模块',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `target_type` varchar(50) DEFAULT NULL COMMENT '目标类型（如：production_order, process等）',
  `target_id` int(11) DEFAULT NULL COMMENT '目标ID',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `old_data` json DEFAULT NULL COMMENT '操作前数据（JSON格式）',
  `new_data` json DEFAULT NULL COMMENT '操作后数据（JSON格式）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  KEY `idx_action` (`action`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表' [ RunTime:0.006703s ]
[2025-07-08T19:01:57+08:00][sql] ALTER TABLE `pp_production_orders`
  ADD CONSTRAINT `fk_production_orders_product` FOREIGN KEY (`product_id`) REFERENCES `pp_products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_production_orders_process` FOREIGN KEY (`current_process_id`) REFERENCES `pp_processes` (`id`) ON DELETE SET NULL ON UPDATE CASCADE [ RunTime:0.017584s ]
[2025-07-08T19:01:57+08:00][sql] ALTER TABLE `pp_production_order_processes`
  ADD CONSTRAINT `fk_order_processes_order` FOREIGN KEY (`production_order_id`) REFERENCES `pp_production_orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_order_processes_process` FOREIGN KEY (`process_id`) REFERENCES `pp_processes` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_order_processes_operator` FOREIGN KEY (`operator_id`) REFERENCES `pp_users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE [ RunTime:0.015540s ]
[2025-07-08T19:01:57+08:00][sql] ALTER TABLE `pp_process_images`
  ADD CONSTRAINT `fk_process_images_order_process` FOREIGN KEY (`production_order_process_id`) REFERENCES `pp_production_order_processes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE [ RunTime:0.013084s ]
[2025-07-08T19:01:57+08:00][sql] ALTER TABLE `pp_user_process_permissions`
  ADD CONSTRAINT `fk_user_process_permissions_user` FOREIGN KEY (`user_id`) REFERENCES `pp_users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_user_process_permissions_process` FOREIGN KEY (`process_id`) REFERENCES `pp_processes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE [ RunTime:0.014234s ]
[2025-07-08T19:01:57+08:00][sql] ALTER TABLE `pp_operation_logs`
  ADD CONSTRAINT `fk_operation_logs_user` FOREIGN KEY (`user_id`) REFERENCES `pp_users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE [ RunTime:0.014774s ]
[2025-07-08T19:01:57+08:00][sql] INSERT INTO `pp_users` (`username`, `password`, `real_name`, `role`, `status`, `phone`, `email`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'admin', 'active', '13800138000', '<EMAIL>'),
('operator1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张三', 'operator', 'active', '13800138001', '<EMAIL>'),
('operator2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李四', 'operator', 'active', '13800138002', '<EMAIL>'),
('operator3', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '王五', 'operator', 'active', '13800138003', '<EMAIL>') [ RunTime:0.001669s ]
[2025-07-08T19:01:57+08:00][sql] INSERT INTO `pp_processes` (`name`, `description`, `sort_order`, `status`, `estimated_time`, `difficulty_level`, `quality_standard`) VALUES
('设计制版', '根据客户需求进行包装设计和制版', 1, 'active', 240, 4, '设计稿需客户确认，制版精度误差不超过0.1mm'),
('印刷', '使用印刷机进行批量印刷', 2, 'active', 120, 3, '色彩饱和度≥95%，套印精度≤0.15mm'),
('覆膜', '在印刷品表面覆膜，增强防水防污能力', 3, 'active', 60, 2, '覆膜平整无气泡，附着力≥2N/cm'),
('模切', '按照设计要求进行模切成型', 4, 'active', 90, 3, '模切精度误差≤0.2mm，边缘光滑无毛刺'),
('糊盒', '将模切好的材料糊制成盒子', 5, 'active', 150, 3, '糊盒牢固，胶水均匀，成型规整'),
('质检', '对成品进行质量检验', 6, 'active', 30, 2, '按照质量标准进行全面检查，合格率≥98%'),
('包装', '对合格产品进行包装', 7, 'active', 45, 1, '包装完整，标识清晰，防护到位') [ RunTime:0.001676s ]
[2025-07-08T19:01:57+08:00][sql] INSERT INTO `pp_products` (`name`, `description`, `abbreviation`, `category`, `specifications`, `material`, `color`, `unit`, `standard_price`, `status`) VALUES
('高档化妆品包装盒', '高端化妆品品牌专用包装盒，精美设计，质感优良', 'HZHZP001', '化妆品包装', '150x100x50mm', '350g铜版纸+哑膜', '粉色', '个', 8.50, 'active'),
('食品包装袋', '食品级安全包装袋，符合国家食品安全标准', 'SPBZD001', '食品包装', '200x300mm', 'PE+PET复合膜', '透明', '个', 0.35, 'active'),
('电子产品包装盒', '数码产品专用包装盒，防震防潮', 'DZCP001', '电子包装', '300x200x100mm', '双瓦楞纸板', '白色', '个', 12.80, 'active'),
('药品包装盒', '医药行业专用包装盒，符合GMP标准', 'YPBZ001', '医药包装', '120x80x30mm', '300g白卡纸', '白色', '个', 2.20, 'active'),
('礼品包装袋', '节日礼品专用包装袋，节庆设计', 'LPBZD001', '礼品包装', '250x350x100mm', '157g铜版纸', '红色', '个', 3.60, 'active'),
('茶叶包装盒', '高档茶叶包装盒，传统工艺制作', 'CYBZ001', '茶叶包装', '200x150x80mm', '特种纸+烫金', '深绿色', '个', 15.20, 'active'),
('酒类包装盒', '白酒红酒专用包装盒，豪华装', 'JLBZ001', '酒类包装', '350x120x120mm', '灰板纸+特种纸', '金色', '个', 25.80, 'active') [ RunTime:0.001276s ]
[2025-07-08T19:01:57+08:00][sql] INSERT INTO `pp_production_orders` (`order_no`, `product_id`, `quantity`, `plan_date`, `delivery_date`, `sort_order`, `status`, `customer_name`, `customer_contact`, `urgent_level`, `remark`) VALUES
('PO202507080001', 1, 5000, '2025-07-09', '2025-07-15', 1, 'pending', '美丽化妆品有限公司', '张经理 13900139001', 3, '客户要求包装精美，注意质量控制'),
('PO202507080002', 3, 2000, '2025-07-10', '2025-07-18', 2, 'pending', '科技数码公司', '李总 13900139002', 2, '电子产品包装，要求防震性能好'),
('PO202507080003', 5, 8000, '2025-07-08', '2025-07-12', 0, 'running', '节日礼品商城', '王主管 13900139003', 4, '春节礼品袋，加急订单') [ RunTime:0.002007s ]
[2025-07-08T19:01:57+08:00][sql] INSERT INTO `pp_production_order_processes` (`production_order_id`, `process_id`, `sort_order`, `status`, `estimated_time`) VALUES

(1, 1, 1, 'pending', 240),  
(1, 2, 2, 'pending', 120),  
(1, 3, 3, 'pending', 60),   
(1, 4, 4, 'pending', 90),   
(1, 5, 5, 'pending', 150),  
(1, 6, 6, 'pending', 30),   
(1, 7, 7, 'pending', 45),   


(2, 1, 1, 'pending', 240),  
(2, 2, 2, 'pending', 120),  
(2, 4, 3, 'pending', 90),   
(2, 5, 4, 'pending', 150),  
(2, 6, 5, 'pending', 30),   
(2, 7, 6, 'pending', 45),   


(3, 1, 1, 'completed', 240), 
(3, 2, 2, 'running', 120),   
(3, 3, 3, 'pending', 60),    
(3, 4, 4, 'pending', 90),    
(3, 5, 5, 'pending', 150),   
(3, 6, 6, 'pending', 30),    
(3, 7, 7, 'pending', 45) [ RunTime:0.002673s ]
[2025-07-08T19:01:57+08:00][sql] UPDATE `pp_production_orders` SET `current_process_id` = 1 WHERE `id` = 1 [ RunTime:0.001302s ]
[2025-07-08T19:01:57+08:00][sql] UPDATE `pp_production_orders` SET `current_process_id` = 1 WHERE `id` = 2 [ RunTime:0.000222s ]
[2025-07-08T19:01:57+08:00][sql] UPDATE `pp_production_orders` SET `current_process_id` = 2, `progress` = 14.29 WHERE `id` = 3 [ RunTime:0.000215s ]
[2025-07-08T19:01:57+08:00][sql] COMMIT [ RunTime:0.000513s ]
[2025-07-08T19:01:57+08:00][sql] SELECT 'Database initialization completed successfully!' as Result [ RunTime:0.000141s ]
[2025-07-08T19:04:00+08:00][sql] CONNECT:[ UseTime:0.020394s ] mysql:host=127.0.0.1;port=3306;dbname=prodsys;charset=utf8mb4
[2025-07-08T19:04:00+08:00][sql] SELECT 1 [ RunTime:0.000241s ]
[2025-07-08T19:04:00+08:00][sql] SHOW TABLES LIKE 'pp_users' [ RunTime:0.000546s ]
[2025-07-08T19:04:00+08:00][sql] SHOW TABLES LIKE 'pp_processes' [ RunTime:0.000303s ]
[2025-07-08T19:04:00+08:00][sql] SHOW TABLES LIKE 'pp_products' [ RunTime:0.000277s ]
[2025-07-08T19:04:00+08:00][sql] SHOW TABLES LIKE 'pp_production_orders' [ RunTime:0.000232s ]
[2025-07-08T19:04:00+08:00][sql] SHOW TABLES LIKE 'pp_production_order_processes' [ RunTime:0.000258s ]
[2025-07-08T19:04:00+08:00][sql] SHOW TABLES LIKE 'pp_process_images' [ RunTime:0.000223s ]
[2025-07-08T19:04:00+08:00][sql] SHOW TABLES LIKE 'pp_user_process_permissions' [ RunTime:0.000201s ]
[2025-07-08T19:04:00+08:00][sql] SHOW TABLES LIKE 'pp_operation_logs' [ RunTime:0.000193s ]
[2025-07-08T19:05:07+08:00][sql] SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO" [ RunTime:0.000256s ]
[2025-07-08T19:05:07+08:00][sql] SET AUTOCOMMIT = 0 [ RunTime:0.000154s ]
[2025-07-08T19:05:07+08:00][sql] START TRANSACTION [ RunTime:0.000160s ]
[2025-07-08T19:05:07+08:00][sql] SET time_zone = "+00:00" [ RunTime:0.000180s ]
[2025-07-08T19:05:07+08:00][sql] DROP TABLE IF EXISTS `pp_process_images` [ RunTime:0.002486s ]
[2025-07-08T19:05:07+08:00][sql] CREATE TABLE `pp_process_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `production_order_process_id` int(11) NOT NULL COMMENT '排产单工序ID',
  `image_path` varchar(500) NOT NULL COMMENT '图片路径',
  `image_name` varchar(200) DEFAULT NULL COMMENT '图片名称',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小（字节）',
  `image_type` varchar(20) DEFAULT NULL COMMENT '图片类型（jpg, png, gif等）',
  `width` int(11) DEFAULT NULL COMMENT '图片宽度',
  `height` int(11) DEFAULT NULL COMMENT '图片高度',
  `description` varchar(500) DEFAULT NULL COMMENT '图片描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_production_order_process_id` (`production_order_process_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工序图片表' [ RunTime:0.005911s ]
[2025-07-08T19:05:07+08:00][sql] DROP TABLE IF EXISTS `pp_user_process_permissions` [ RunTime:0.002761s ]
[2025-07-08T19:05:07+08:00][sql] CREATE TABLE `pp_user_process_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `process_id` int(11) NOT NULL COMMENT '工序ID',
  `permission_type` enum('read','write','execute') NOT NULL DEFAULT 'execute' COMMENT '权限类型：read-查看，write-编辑，execute-执行',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_process_permission` (`user_id`, `process_id`, `permission_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_process_id` (`process_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户工序权限表' [ RunTime:0.006728s ]
[2025-07-08T19:05:07+08:00][sql] DROP TABLE IF EXISTS `pp_operation_logs` [ RunTime:0.002363s ]
[2025-07-08T19:05:07+08:00][sql] CREATE TABLE `pp_operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) DEFAULT NULL COMMENT '操作用户ID',
  `module` varchar(50) NOT NULL COMMENT '操作模块',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `target_type` varchar(50) DEFAULT NULL COMMENT '目标类型（如：production_order, process等）',
  `target_id` int(11) DEFAULT NULL COMMENT '目标ID',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `old_data` json DEFAULT NULL COMMENT '操作前数据（JSON格式）',
  `new_data` json DEFAULT NULL COMMENT '操作后数据（JSON格式）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  KEY `idx_action` (`action`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表' [ RunTime:0.007206s ]
[2025-07-08T19:05:07+08:00][sql] ALTER TABLE `pp_process_images`
  ADD CONSTRAINT `fk_process_images_order_process` FOREIGN KEY (`production_order_process_id`) REFERENCES `pp_production_order_processes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE [ RunTime:0.014450s ]
[2025-07-08T19:05:07+08:00][sql] ALTER TABLE `pp_user_process_permissions`
  ADD CONSTRAINT `fk_user_process_permissions_user` FOREIGN KEY (`user_id`) REFERENCES `pp_users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_user_process_permissions_process` FOREIGN KEY (`process_id`) REFERENCES `pp_processes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE [ RunTime:0.014459s ]
[2025-07-08T19:05:07+08:00][sql] ALTER TABLE `pp_operation_logs`
  ADD CONSTRAINT `fk_operation_logs_user` FOREIGN KEY (`user_id`) REFERENCES `pp_users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE [ RunTime:0.015287s ]
[2025-07-08T19:05:07+08:00][sql] INSERT INTO `pp_processes` (`name`, `description`, `sort_order`, `status`, `estimated_time`, `difficulty_level`, `quality_standard`) VALUES
('设计制版', '根据客户需求进行包装设计和制版', 1, 'active', 240, 4, '设计稿需客户确认，制版精度误差不超过0.1mm'),
('印刷', '使用印刷机进行批量印刷', 2, 'active', 120, 3, '色彩饱和度≥95%，套印精度≤0.15mm'),
('覆膜', '在印刷品表面覆膜，增强防水防污能力', 3, 'active', 60, 2, '覆膜平整无气泡，附着力≥2N/cm'),
('模切', '按照设计要求进行模切成型', 4, 'active', 90, 3, '模切精度误差≤0.2mm，边缘光滑无毛刺'),
('糊盒', '将模切好的材料糊制成盒子', 5, 'active', 150, 3, '糊盒牢固，胶水均匀，成型规整'),
('质检', '对成品进行质量检验', 6, 'active', 30, 2, '按照质量标准进行全面检查，合格率≥98%'),
('包装', '对合格产品进行包装', 7, 'active', 45, 1, '包装完整，标识清晰，防护到位') [ RunTime:0.000282s ]
[2025-07-08T19:05:07+08:00][sql] UPDATE `pp_production_orders` SET `current_process_id` = 1 WHERE `id` = 1 [ RunTime:0.000184s ]
[2025-07-08T19:05:07+08:00][sql] UPDATE `pp_production_orders` SET `current_process_id` = 1 WHERE `id` = 2 [ RunTime:0.000180s ]
[2025-07-08T19:05:07+08:00][sql] UPDATE `pp_production_orders` SET `current_process_id` = 2, `progress` = 14.29 WHERE `id` = 3 [ RunTime:0.000177s ]
[2025-07-08T19:05:07+08:00][sql] COMMIT [ RunTime:0.000581s ]
[2025-07-08T19:05:07+08:00][sql] SELECT 'Database initialization completed successfully!' as Result [ RunTime:0.000150s ]
[2025-07-08T19:08:15+08:00][sql] CONNECT:[ UseTime:0.001803s ] mysql:host=127.0.0.1;port=3306;dbname=prodsys;charset=utf8mb4
[2025-07-08T19:08:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_users` [ RunTime:0.001401s ]
[2025-07-08T19:08:15+08:00][sql] SELECT COUNT(*) AS think_count FROM `pp_users` [ RunTime:0.038164s ]
[2025-07-08T19:08:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_processes` [ RunTime:0.001300s ]
[2025-07-08T19:08:15+08:00][sql] SELECT COUNT(*) AS think_count FROM `pp_processes` [ RunTime:0.000184s ]
[2025-07-08T19:08:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_products` [ RunTime:0.001121s ]
[2025-07-08T19:08:15+08:00][sql] SELECT COUNT(*) AS think_count FROM `pp_products` [ RunTime:0.000179s ]
[2025-07-08T19:08:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_production_orders` [ RunTime:0.001111s ]
[2025-07-08T19:08:15+08:00][sql] SELECT COUNT(*) AS think_count FROM `pp_production_orders` [ RunTime:0.000182s ]
[2025-07-08T19:08:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_production_order_processes` [ RunTime:0.001216s ]
[2025-07-08T19:08:15+08:00][sql] SELECT COUNT(*) AS think_count FROM `pp_production_order_processes` [ RunTime:0.000201s ]
[2025-07-08T19:08:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_process_images` [ RunTime:0.001163s ]
[2025-07-08T19:08:15+08:00][sql] SELECT COUNT(*) AS think_count FROM `pp_process_images` [ RunTime:0.000173s ]
[2025-07-08T19:08:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_user_process_permissions` [ RunTime:0.001126s ]
[2025-07-08T19:08:15+08:00][sql] SELECT COUNT(*) AS think_count FROM `pp_user_process_permissions` [ RunTime:0.000164s ]
[2025-07-08T19:08:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_operation_logs` [ RunTime:0.001238s ]
[2025-07-08T19:08:15+08:00][sql] SELECT COUNT(*) AS think_count FROM `pp_operation_logs` [ RunTime:0.000185s ]
[2025-07-08T19:08:15+08:00][sql] SELECT `username`,`real_name`,`role` FROM `pp_users` [ RunTime:0.000506s ]
[2025-07-08T19:08:15+08:00][sql] SELECT `name`,`sort_order` FROM `pp_processes` ORDER BY `sort_order` [ RunTime:0.000231s ]
[2025-07-08T19:08:15+08:00][sql] SELECT `name`,`abbreviation` FROM `pp_products` [ RunTime:0.000204s ]
[2025-07-08T19:13:21+08:00][sql] CONNECT:[ UseTime:0.001697s ] mysql:host=127.0.0.1;port=3306;dbname=prodsys;charset=utf8mb4
[2025-07-08T19:13:21+08:00][sql] SHOW FULL COLUMNS FROM `pp_users` [ RunTime:0.001299s ]
[2025-07-08T19:13:21+08:00][sql] SELECT * FROM `pp_users` WHERE (  `username` = 'admin'  AND `deleted_at` IS NULL ) AND `pp_users`.`deleted_at` IS NULL LIMIT 1 [ RunTime:0.000274s ]
[2025-07-08T19:13:21+08:00][sql] SELECT * FROM `pp_users` WHERE (  `role` = 'operator'  AND `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_users`.`deleted_at` IS NULL [ RunTime:0.038612s ]
[2025-07-08T19:13:21+08:00][error] [64]Cannot make non static method think\Model::getOptions() static in class app\common\model\Process[E:\项目开发\印刷厂排产\printing-production-system\app\common\model\Process.php:40]
[2025-07-08T19:14:15+08:00][sql] CONNECT:[ UseTime:0.015650s ] mysql:host=127.0.0.1;port=3306;dbname=prodsys;charset=utf8mb4
[2025-07-08T19:14:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_users` [ RunTime:0.001338s ]
[2025-07-08T19:14:15+08:00][sql] SELECT * FROM `pp_users` WHERE (  `username` = 'admin'  AND `deleted_at` IS NULL ) AND `pp_users`.`deleted_at` IS NULL LIMIT 1 [ RunTime:0.000249s ]
[2025-07-08T19:14:15+08:00][sql] SELECT * FROM `pp_users` WHERE (  `role` = 'operator'  AND `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_users`.`deleted_at` IS NULL [ RunTime:0.000300s ]
[2025-07-08T19:14:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_processes` [ RunTime:0.001455s ]
[2025-07-08T19:14:15+08:00][sql] SELECT * FROM `pp_processes` WHERE (  `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_processes`.`deleted_at` IS NULL ORDER BY `sort_order` ASC [ RunTime:0.000490s ]
[2025-07-08T19:14:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_products` [ RunTime:0.001178s ]
[2025-07-08T19:14:15+08:00][sql] SELECT * FROM `pp_products` WHERE (  `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_products`.`deleted_at` IS NULL ORDER BY `created_at` DESC [ RunTime:0.000260s ]
[2025-07-08T19:14:15+08:00][sql] SHOW FULL COLUMNS FROM `pp_production_orders` [ RunTime:0.001208s ]
[2025-07-08T19:15:51+08:00][sql] CONNECT:[ UseTime:0.019796s ] mysql:host=127.0.0.1;port=3306;dbname=prodsys;charset=utf8mb4
[2025-07-08T19:15:51+08:00][sql] SHOW FULL COLUMNS FROM `pp_users` [ RunTime:0.001370s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_users` WHERE (  `username` = 'admin'  AND `deleted_at` IS NULL ) AND `pp_users`.`deleted_at` IS NULL LIMIT 1 [ RunTime:0.000246s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_users` WHERE (  `role` = 'operator'  AND `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_users`.`deleted_at` IS NULL [ RunTime:0.000245s ]
[2025-07-08T19:15:51+08:00][sql] SHOW FULL COLUMNS FROM `pp_processes` [ RunTime:0.001153s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_processes` WHERE (  `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_processes`.`deleted_at` IS NULL ORDER BY `sort_order` ASC [ RunTime:0.000268s ]
[2025-07-08T19:15:51+08:00][sql] SHOW FULL COLUMNS FROM `pp_products` [ RunTime:0.001816s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_products` WHERE (  `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_products`.`deleted_at` IS NULL ORDER BY `created_at` DESC [ RunTime:0.000361s ]
[2025-07-08T19:15:51+08:00][sql] SHOW FULL COLUMNS FROM `pp_production_orders` [ RunTime:0.001588s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_production_orders` [ RunTime:0.000226s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_products` WHERE (  (  `id` IN (1,3,5) ) ) AND `pp_products`.`deleted_at` IS NULL [ RunTime:0.000251s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_processes` WHERE (  (  `id` IN (1,2) ) ) AND `pp_processes`.`deleted_at` IS NULL [ RunTime:0.000296s ]
[2025-07-08T19:15:51+08:00][sql] SHOW FULL COLUMNS FROM `pp_production_order_processes` [ RunTime:0.001238s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_production_order_processes` [ RunTime:0.000206s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_production_orders` WHERE  (  `id` IN (1,2,3) ) [ RunTime:0.000222s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_processes` WHERE (  (  `id` IN (1,2,3,4,5,6,7) ) ) AND `pp_processes`.`deleted_at` IS NULL [ RunTime:0.000256s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_products` WHERE (  `abbreviation` LIKE '%HZ%'  AND `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_products`.`deleted_at` IS NULL LIMIT 10 [ RunTime:0.000308s ]
[2025-07-08T19:15:51+08:00][sql] SELECT * FROM `pp_processes` WHERE (  `status` = 'active'  AND `deleted_at` IS NULL ) AND `pp_processes`.`deleted_at` IS NULL ORDER BY `sort_order` ASC [ RunTime:0.000294s ]
[2025-07-08T20:50:32+08:00][sql] CONNECT:[ UseTime:0.001833s ] mysql:host=127.0.0.1;port=3306;dbname=prodsys;charset=utf8mb4
[2025-07-08T20:50:32+08:00][sql] SHOW FULL COLUMNS FROM `pp_users` [ RunTime:0.001342s ]
[2025-07-08T20:50:32+08:00][sql] SELECT * FROM `pp_users` WHERE (  `username` = 'admin'  AND `deleted_at` IS NULL ) AND `pp_users`.`deleted_at` IS NULL LIMIT 1 [ RunTime:0.000303s ]
[2025-07-08T20:50:33+08:00][sql] UPDATE `pp_users`  SET `last_login_time` = '2025-07-08 20:50:33' , `last_login_ip` = '0.0.0.0'  WHERE (  `id` = 1 ) AND `pp_users`.`deleted_at` IS NULL [ RunTime:0.038062s ]
[2025-07-08T20:50:33+08:00][sql] SHOW FULL COLUMNS FROM `pp_operation_logs` [ RunTime:0.001631s ]
[2025-07-08T20:50:33+08:00][sql] INSERT INTO `pp_operation_logs` SET `user_id` = 1 , `module` = 'user' , `action` = 'login' , `target_type` = '' , `target_id` = 0 , `description` = '管理员登录' , `old_data` = NULL , `new_data` = '{\"id\":1,\"username\":\"admin\",\"real_name\":\"\\u7cfb\\u7edf\\u7ba1\\u7406\\u5458\",\"role\":\"admin\",\"avatar\":null,\"login_time\":1751979033}' , `ip_address` = '0.0.0.0' , `user_agent` = NULL , `created_at` = '2025-07-08 20:50:33.067432' [ RunTime:0.001429s ]
