<?php
declare (strict_types = 1);

namespace app\common\model;

/**
 * 产品模型
 */
class Product extends BaseModel
{
    // 表名
    protected $name = 'products';
    
    // 类型转换
    protected $type = [
        'standard_price' => 'float',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp',
        'deleted_at' => 'timestamp',
    ];
    
    /**
     * 获取启用的产品列表
     * @return \think\Collection
     */
    public static function getActiveList()
    {
        return self::where('status', self::STATUS_ACTIVE)
                   ->where('deleted_at', null)
                   ->order('created_at', 'desc')
                   ->select();
    }
    
    /**
     * 获取产品选项（用于下拉框）
     * @return array
     */
    public static function getProductOptions()
    {
        $list = self::getActiveList();
        $options = [];
        foreach ($list as $item) {
            $options[$item->id] = $item->name . ' (' . $item->abbreviation . ')';
        }
        return $options;
    }
    
    /**
     * 根据缩写搜索产品
     * @param string $abbreviation
     * @return \think\Collection
     */
    public static function searchByAbbreviation($abbreviation)
    {
        return self::where('abbreviation', 'like', '%' . $abbreviation . '%')
                   ->where('status', self::STATUS_ACTIVE)
                   ->where('deleted_at', null)
                   ->limit(10)
                   ->select();
    }
    
    /**
     * 根据产品名称搜索
     * @param string $name
     * @return \think\Collection
     */
    public static function searchByName($name)
    {
        return self::where('name', 'like', '%' . $name . '%')
                   ->where('status', self::STATUS_ACTIVE)
                   ->where('deleted_at', null)
                   ->limit(10)
                   ->select();
    }
    
    /**
     * 获取产品分类列表
     * @return array
     */
    public static function getCategories()
    {
        return self::where('deleted_at', null)
                   ->where('category', '<>', '')
                   ->group('category')
                   ->column('category');
    }
    
    /**
     * 检查缩写是否唯一
     * @param string $abbreviation
     * @param int $excludeId 排除的ID（用于编辑时检查）
     * @return bool
     */
    public static function isAbbreviationUnique($abbreviation, $excludeId = 0)
    {
        $query = self::where('abbreviation', $abbreviation)
                     ->where('deleted_at', null);
        
        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() === 0;
    }
    
    /**
     * 检查产品是否可以删除
     * @return bool
     */
    public function canDelete()
    {
        // 检查是否有排产单正在使用此产品
        $count = ProductionOrder::where('product_id', $this->id)->count();
        return $count === 0;
    }
    
    /**
     * 获取产品统计信息
     * @return array
     */
    public function getStatistics()
    {
        // 获取使用此产品的排产单数量
        $totalOrders = ProductionOrder::where('product_id', $this->id)->count();
        
        // 获取已完成的排产单数量
        $completedOrders = ProductionOrder::where('product_id', $this->id)
                                          ->where('status', 'completed')
                                          ->count();
        
        // 获取总生产数量
        $totalQuantity = ProductionOrder::where('product_id', $this->id)->sum('quantity');
        
        // 获取已完成的生产数量
        $completedQuantity = ProductionOrder::where('product_id', $this->id)
                                            ->where('status', 'completed')
                                            ->sum('total_qualified_quantity');
        
        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'total_quantity' => $totalQuantity,
            'completed_quantity' => $completedQuantity,
            'completion_rate' => $totalOrders > 0 ? round($completedOrders / $totalOrders * 100, 2) : 0,
        ];
    }
    
    /**
     * 格式化价格显示
     * @return string
     */
    public function getFormattedPrice()
    {
        return $this->standard_price ? '¥' . number_format($this->standard_price, 2) : '未设置';
    }
}
