<?php
declare (strict_types = 1);

namespace app\common\model;

/**
 * 工序图片模型
 */
class ProcessImage extends BaseModel
{
    // 表名
    protected $name = 'process_images';

    // 关闭自动时间戳（只有created_at）
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'created_at';
    protected $updateTime = false;

    // 工序图片表没有软删除字段
    protected $deleteTime = false;
    
    // 类型转换
    protected $type = [
        'file_size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'timestamp',
    ];
    
    /**
     * 关联排产单工序
     * @return \think\model\relation\BelongsTo
     */
    public function productionOrderProcess()
    {
        return $this->belongsTo(ProductionOrderProcess::class, 'production_order_process_id');
    }
    
    /**
     * 获取图片完整URL
     * @return string
     */
    public function getImageUrl()
    {
        if (empty($this->image_path)) {
            return '';
        }
        
        // 如果是完整URL，直接返回
        if (strpos($this->image_path, 'http') === 0) {
            return $this->image_path;
        }
        
        // 拼接域名
        $domain = request()->domain();
        return $domain . '/' . ltrim($this->image_path, '/');
    }
    
    /**
     * 获取缩略图URL
     * @param string $size 尺寸，如 '200x200'
     * @return string
     */
    public function getThumbnailUrl($size = '200x200')
    {
        $imageUrl = $this->getImageUrl();
        if (empty($imageUrl)) {
            return '';
        }
        
        // 这里可以集成图片处理服务，如阿里云OSS的图片处理
        // 暂时返回原图
        return $imageUrl;
    }
    
    /**
     * 格式化文件大小
     * @return string
     */
    public function getFormattedFileSize()
    {
        if (!$this->file_size) {
            return '未知';
        }
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $index = 0;
        
        while ($size >= 1024 && $index < count($units) - 1) {
            $size /= 1024;
            $index++;
        }
        
        return round($size, 2) . ' ' . $units[$index];
    }
    
    /**
     * 批量保存图片
     * @param int $productionOrderProcessId
     * @param array $images 图片信息数组
     * @return bool
     */
    public static function saveBatch($productionOrderProcessId, $images)
    {
        try {
            self::startTrans();
            
            $sortOrder = self::where('production_order_process_id', $productionOrderProcessId)
                             ->max('sort_order') ?: 0;
            
            foreach ($images as $image) {
                $sortOrder++;
                self::create([
                    'production_order_process_id' => $productionOrderProcessId,
                    'image_path' => $image['path'],
                    'image_name' => $image['name'] ?? '',
                    'file_size' => $image['size'] ?? 0,
                    'image_type' => $image['type'] ?? '',
                    'width' => $image['width'] ?? 0,
                    'height' => $image['height'] ?? 0,
                    'description' => $image['description'] ?? '',
                    'sort_order' => $sortOrder,
                ]);
            }
            
            self::commit();
            return true;
        } catch (\Exception $e) {
            self::rollback();
            return false;
        }
    }
    
    /**
     * 删除图片文件
     * @return bool
     */
    public function deleteFile()
    {
        if (empty($this->image_path)) {
            return true;
        }
        
        $filePath = public_path() . $this->image_path;
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }
    
    /**
     * 删除记录时同时删除文件
     * @return bool
     */
    public function delete()
    {
        $this->deleteFile();
        return parent::delete();
    }
    
    /**
     * 更新排序
     * @param array $sortData 格式：[['id' => 1, 'sort_order' => 1], ...]
     * @return bool
     */
    public static function updateSort($sortData)
    {
        try {
            self::startTrans();
            foreach ($sortData as $item) {
                self::where('id', $item['id'])->update(['sort_order' => $item['sort_order']]);
            }
            self::commit();
            return true;
        } catch (\Exception $e) {
            self::rollback();
            return false;
        }
    }
    
    /**
     * 获取工序的所有图片
     * @param int $productionOrderProcessId
     * @return \think\Collection
     */
    public static function getProcessImages($productionOrderProcessId)
    {
        return self::where('production_order_process_id', $productionOrderProcessId)
                   ->order('sort_order', 'asc')
                   ->select();
    }
}
