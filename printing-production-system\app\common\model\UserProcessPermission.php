<?php
declare (strict_types = 1);

namespace app\common\model;

/**
 * 用户工序权限模型
 */
class UserProcessPermission extends BaseModel
{
    // 表名
    protected $name = 'user_process_permissions';

    // 用户工序权限表没有软删除字段
    protected $deleteTime = false;
    
    // 权限类型常量
    const PERMISSION_READ = 'read';
    const PERMISSION_WRITE = 'write';
    const PERMISSION_EXECUTE = 'execute';
    
    // 权限文本映射
    protected $permissionText = [
        self::PERMISSION_READ => '查看',
        self::PERMISSION_WRITE => '编辑',
        self::PERMISSION_EXECUTE => '执行',
    ];
    
    // 类型转换
    protected $type = [
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp',
    ];
    
    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 关联工序
     * @return \think\model\relation\BelongsTo
     */
    public function process()
    {
        return $this->belongsTo(Process::class, 'process_id');
    }
    
    /**
     * 获取权限文本
     * @param string $permission
     * @return string
     */
    public function getPermissionText($permission = '')
    {
        $permission = $permission ?: $this->permission_type;
        return $this->permissionText[$permission] ?? '未知';
    }
    
    /**
     * 批量设置用户工序权限
     * @param int $userId
     * @param array $processIds
     * @param string $permissionType
     * @return bool
     */
    public static function setUserPermissions($userId, $processIds, $permissionType = self::PERMISSION_EXECUTE)
    {
        try {
            self::startTrans();
            
            // 删除用户现有的该类型权限
            self::where('user_id', $userId)
                ->where('permission_type', $permissionType)
                ->delete();
            
            // 添加新权限
            foreach ($processIds as $processId) {
                self::create([
                    'user_id' => $userId,
                    'process_id' => $processId,
                    'permission_type' => $permissionType,
                ]);
            }
            
            self::commit();
            return true;
        } catch (\Exception $e) {
            self::rollback();
            return false;
        }
    }
    
    /**
     * 获取用户的工序权限
     * @param int $userId
     * @param string $permissionType
     * @return array 工序ID数组
     */
    public static function getUserProcessIds($userId, $permissionType = self::PERMISSION_EXECUTE)
    {
        return self::where('user_id', $userId)
                   ->where('permission_type', $permissionType)
                   ->column('process_id');
    }
    
    /**
     * 获取工序的操作员
     * @param int $processId
     * @param string $permissionType
     * @return \think\Collection
     */
    public static function getProcessOperators($processId, $permissionType = self::PERMISSION_EXECUTE)
    {
        return User::alias('u')
                   ->join('user_process_permissions upp', 'u.id = upp.user_id')
                   ->where('upp.process_id', $processId)
                   ->where('upp.permission_type', $permissionType)
                   ->where('u.role', User::ROLE_OPERATOR)
                   ->where('u.status', User::STATUS_ACTIVE)
                   ->where('u.deleted_at', null)
                   ->field('u.*')
                   ->select();
    }
    
    /**
     * 检查用户是否有工序权限
     * @param int $userId
     * @param int $processId
     * @param string $permissionType
     * @return bool
     */
    public static function hasPermission($userId, $processId, $permissionType = self::PERMISSION_EXECUTE)
    {
        // 管理员拥有所有权限
        $user = User::find($userId);
        if ($user && $user->role === User::ROLE_ADMIN) {
            return true;
        }
        
        return self::where('user_id', $userId)
                   ->where('process_id', $processId)
                   ->where('permission_type', $permissionType)
                   ->count() > 0;
    }
    
    /**
     * 获取用户权限列表（用于显示）
     * @param int $userId
     * @return array
     */
    public static function getUserPermissionList($userId)
    {
        $permissions = self::alias('upp')
                          ->join('processes p', 'upp.process_id = p.id')
                          ->where('upp.user_id', $userId)
                          ->where('p.deleted_at', null)
                          ->field('upp.*, p.name as process_name')
                          ->order('p.sort_order', 'asc')
                          ->select();
        
        $result = [];
        foreach ($permissions as $permission) {
            $processId = $permission['process_id'];
            if (!isset($result[$processId])) {
                $result[$processId] = [
                    'process_id' => $processId,
                    'process_name' => $permission['process_name'],
                    'permissions' => [],
                ];
            }
            $result[$processId]['permissions'][] = $permission['permission_type'];
        }
        
        return array_values($result);
    }
    
    /**
     * 复制用户权限
     * @param int $fromUserId 源用户ID
     * @param int $toUserId 目标用户ID
     * @return bool
     */
    public static function copyUserPermissions($fromUserId, $toUserId)
    {
        try {
            self::startTrans();
            
            // 删除目标用户现有权限
            self::where('user_id', $toUserId)->delete();
            
            // 复制源用户权限
            $permissions = self::where('user_id', $fromUserId)->select();
            foreach ($permissions as $permission) {
                self::create([
                    'user_id' => $toUserId,
                    'process_id' => $permission['process_id'],
                    'permission_type' => $permission['permission_type'],
                ]);
            }
            
            self::commit();
            return true;
        } catch (\Exception $e) {
            self::rollback();
            return false;
        }
    }
}
