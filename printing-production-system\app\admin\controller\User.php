<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\model\User as UserModel;
use app\common\model\OperationLog;

/**
 * 用户管理控制器
 */
class User extends BaseController
{
    /**
     * 用户列表
     * @return string
     */
    public function index()
    {
        $keyword = $this->request->get('keyword', '');
        $role = $this->request->get('role', '');
        $status = $this->request->get('status', '');

        $where = [];
        if (!empty($keyword)) {
            $where[] = ['username|real_name', 'like', '%' . $keyword . '%'];
        }
        if (!empty($role)) {
            $where[] = ['role', '=', $role];
        }
        if (!empty($status)) {
            $where[] = ['status', '=', $status];
        }

        $users = UserModel::getList($where, 15);

        return $this->renderUserList($users, compact('keyword', 'role', 'status'));
    }

    /**
     * 添加用户页面
     * @return string
     */
    public function create()
    {
        return $this->renderUserForm();
    }

    /**
     * 保存用户
     * @return \think\response\Json
     */
    public function save()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方法不允许');
        }

        $data = $this->request->post();

        try {
            // 验证数据
            $this->validate($data, [
                'username' => 'require|alphaNum|length:3,20',
                'password' => 'require|length:6,20',
                'real_name' => 'require|length:2,10',
                'role' => 'require|in:admin,operator',
                'status' => 'require|in:active,inactive',
                'phone' => 'mobile',
                'email' => 'email',
            ], [
                'username.require' => '用户名不能为空',
                'username.alphaNum' => '用户名只能包含字母和数字',
                'username.length' => '用户名长度为3-20个字符',
                'password.require' => '密码不能为空',
                'password.length' => '密码长度为6-20个字符',
                'real_name.require' => '真实姓名不能为空',
                'real_name.length' => '真实姓名长度为2-10个字符',
                'role.require' => '角色不能为空',
                'role.in' => '角色值无效',
                'status.require' => '状态不能为空',
                'status.in' => '状态值无效',
                'phone.mobile' => '手机号格式不正确',
                'email.email' => '邮箱格式不正确',
            ]);

            // 检查用户名是否已存在
            if (UserModel::where('username', $data['username'])->where('deleted_at', null)->count() > 0) {
                return $this->error('用户名已存在');
            }

            // 创建用户
            $user = UserModel::create($data);

            // 记录操作日志
            OperationLog::log('user', 'create', '创建用户：' . $data['username'], null, $data, 'user', $user->id);

            return $this->success('用户创建成功', ['id' => $user->id]);

        } catch (\Exception $e) {
            return $this->error('创建失败：' . $e->getMessage());
        }
    }

    /**
     * 编辑用户页面
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        $user = UserModel::find($id);
        if (!$user) {
            return '用户不存在';
        }

        return $this->renderUserForm($user);
    }

    /**
     * 更新用户
     * @param int $id
     * @return \think\response\Json
     */
    public function update($id)
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方法不允许');
        }

        $user = UserModel::find($id);
        if (!$user) {
            return $this->error('用户不存在');
        }

        $data = $this->request->post();
        $oldData = $user->toArray();

        try {
            // 验证数据
            $rules = [
                'real_name' => 'require|length:2,10',
                'role' => 'require|in:admin,operator',
                'status' => 'require|in:active,inactive',
                'phone' => 'mobile',
                'email' => 'email',
            ];

            // 如果修改了用户名，需要验证
            if (isset($data['username']) && $data['username'] !== $user->username) {
                $rules['username'] = 'require|alphaNum|length:3,20';
                // 检查用户名是否已存在
                if (UserModel::where('username', $data['username'])->where('id', '<>', $id)->where('deleted_at', null)->count() > 0) {
                    return $this->error('用户名已存在');
                }
            }

            // 如果修改了密码，需要验证
            if (!empty($data['password'])) {
                $rules['password'] = 'length:6,20';
            } else {
                unset($data['password']); // 不修改密码
            }

            $this->validate($data, $rules);

            // 更新用户
            $user->save($data);

            // 记录操作日志
            OperationLog::log('user', 'update', '更新用户：' . $user->username, $oldData, $data, 'user', $user->id);

            return $this->success('用户更新成功');

        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除用户
     * @param int $id
     * @return \think\response\Json
     */
    public function delete($id)
    {
        if (!$this->request->isDelete()) {
            return $this->error('请求方法不允许');
        }

        $user = UserModel::find($id);
        if (!$user) {
            return $this->error('用户不存在');
        }

        // 不能删除自己
        $currentUser = get_admin_user();
        if ($user->id == $currentUser['id']) {
            return $this->error('不能删除自己');
        }

        try {
            $oldData = $user->toArray();
            $user->delete();

            // 记录操作日志
            OperationLog::log('user', 'delete', '删除用户：' . $user->username, $oldData, null, 'user', $user->id);

            return $this->success('用户删除成功');

        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户状态
     * @param int $id
     * @return \think\response\Json
     */
    public function status($id)
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方法不允许');
        }

        $user = UserModel::find($id);
        if (!$user) {
            return $this->error('用户不存在');
        }

        $status = $this->request->post('status');
        if (!in_array($status, ['active', 'inactive'])) {
            return $this->error('状态值无效');
        }

        // 不能禁用自己
        $currentUser = get_admin_user();
        if ($user->id == $currentUser['id'] && $status === 'inactive') {
            return $this->error('不能禁用自己');
        }

        try {
            $oldStatus = $user->status;
            $user->status = $status;
            $user->save();

            // 记录操作日志
            OperationLog::log('user', 'status', '更新用户状态：' . $user->username . ' ' . $oldStatus . ' -> ' . $status, 
                ['status' => $oldStatus], ['status' => $status], 'user', $user->id);

            return $this->success('状态更新成功');

        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 渲染用户列表页面
     * @param $users
     * @param $params
     * @return string
     */
    private function renderUserList($users, $params)
    {
        // 这里应该渲染用户列表页面
        // 为了简化，先返回基本的HTML
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-people"></i> 用户管理</h2>
            <a href="/admin/user/create" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> 添加用户
            </a>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>真实姓名</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>手机号</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>';

        foreach ($users as $user) {
            $statusBadge = $user->status === 'active' ? 
                '<span class="badge bg-success">启用</span>' : 
                '<span class="badge bg-danger">禁用</span>';
            
            $roleBadge = $user->role === 'admin' ? 
                '<span class="badge bg-primary">管理员</span>' : 
                '<span class="badge bg-info">操作员</span>';

            $html .= '<tr>
                <td>' . $user->id . '</td>
                <td>' . htmlspecialchars($user->username) . '</td>
                <td>' . htmlspecialchars($user->real_name ?: '-') . '</td>
                <td>' . $roleBadge . '</td>
                <td>' . $statusBadge . '</td>
                <td>' . htmlspecialchars($user->phone ?: '-') . '</td>
                <td>' . $user->created_at . '</td>
                <td>
                    <a href="/admin/user/edit/' . $user->id . '" class="btn btn-sm btn-outline-primary">编辑</a>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(' . $user->id . ')">删除</button>
                </td>
            </tr>';
        }

        $html .= '</tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteUser(id) {
            if (confirm("确定要删除这个用户吗？")) {
                fetch("/admin/user/delete/" + id, {
                    method: "DELETE"
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        alert("删除成功");
                        location.reload();
                    } else {
                        alert(data.msg);
                    }
                });
            }
        }
    </script>
</body>
</html>';

        return $html;
    }

    /**
     * 渲染用户表单页面
     * @param $user
     * @return string
     */
    private function renderUserForm($user = null)
    {
        $isEdit = !empty($user);
        $title = $isEdit ? '编辑用户' : '添加用户';
        $action = $isEdit ? '/admin/user/update/' . $user->id : '/admin/user/save';

        return '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $title . '</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid p-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-person"></i> ' . $title . '</h5>
                    </div>
                    <div class="card-body">
                        <form id="userForm">
                            <div class="mb-3">
                                <label class="form-label">用户名 *</label>
                                <input type="text" class="form-control" name="username" value="' . ($user->username ?? '') . '" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">密码 ' . ($isEdit ? '' : '*') . '</label>
                                <input type="password" class="form-control" name="password" ' . ($isEdit ? 'placeholder="留空则不修改"' : 'required') . '>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">真实姓名 *</label>
                                <input type="text" class="form-control" name="real_name" value="' . ($user->real_name ?? '') . '" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">角色 *</label>
                                <select class="form-select" name="role" required>
                                    <option value="">请选择角色</option>
                                    <option value="admin" ' . (($user->role ?? '') === 'admin' ? 'selected' : '') . '>管理员</option>
                                    <option value="operator" ' . (($user->role ?? '') === 'operator' ? 'selected' : '') . '>操作员</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">状态 *</label>
                                <select class="form-select" name="status" required>
                                    <option value="">请选择状态</option>
                                    <option value="active" ' . (($user->status ?? 'active') === 'active' ? 'selected' : '') . '>启用</option>
                                    <option value="inactive" ' . (($user->status ?? '') === 'inactive' ? 'selected' : '') . '>禁用</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">手机号</label>
                                <input type="text" class="form-control" name="phone" value="' . ($user->phone ?? '') . '">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">邮箱</label>
                                <input type="email" class="form-control" name="email" value="' . ($user->email ?? '') . '">
                            </div>
                            <div class="d-flex justify-content-between">
                                <a href="/admin/user" class="btn btn-secondary">返回</a>
                                <button type="submit" class="btn btn-primary">保存</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById("userForm").addEventListener("submit", function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector("button[type=submit]");
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = "保存中...";
            submitBtn.disabled = true;
            
            fetch("' . $action . '", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert("保存成功");
                    window.location.href = "/admin/user";
                } else {
                    alert(data.msg);
                }
            })
            .catch(error => {
                alert("保存失败：" + error.message);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>';
    }
}
