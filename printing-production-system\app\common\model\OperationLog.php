<?php
declare (strict_types = 1);

namespace app\common\model;

/**
 * 操作日志模型
 */
class OperationLog extends BaseModel
{
    // 表名
    protected $name = 'operation_logs';

    // 关闭自动时间戳（只有created_at）
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'created_at';
    protected $updateTime = false;

    // 操作日志表没有软删除字段
    protected $deleteTime = false;
    
    // 类型转换
    protected $type = [
        'target_id' => 'integer',
        'old_data' => 'json',
        'new_data' => 'json',
        'created_at' => 'timestamp',
    ];
    
    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 记录操作日志
     * @param array $data 日志数据
     * @return bool
     */
    public static function record($data)
    {
        try {
            // 获取当前用户信息
            $user = session('admin_user') ?: session('operator_user');
            $request = request();
            
            $logData = [
                'user_id' => $user['id'] ?? null,
                'module' => $data['module'],
                'action' => $data['action'],
                'target_type' => $data['target_type'] ?? '',
                'target_id' => $data['target_id'] ?? 0,
                'description' => $data['description'] ?? '',
                'old_data' => $data['old_data'] ?? null,
                'new_data' => $data['new_data'] ?? null,
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
            ];
            
            return self::create($logData) ? true : false;
        } catch (\Exception $e) {
            // 日志记录失败不应该影响主业务
            return false;
        }
    }
    
    /**
     * 记录用户操作
     * @param string $module 模块名
     * @param string $action 操作名
     * @param string $description 描述
     * @param array $oldData 操作前数据
     * @param array $newData 操作后数据
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     * @return bool
     */
    public static function log($module, $action, $description = '', $oldData = null, $newData = null, $targetType = '', $targetId = 0)
    {
        return self::record([
            'module' => $module,
            'action' => $action,
            'description' => $description,
            'old_data' => $oldData,
            'new_data' => $newData,
            'target_type' => $targetType,
            'target_id' => $targetId,
        ]);
    }
    
    /**
     * 获取操作日志列表
     * @param array $where 查询条件
     * @param int $limit 每页数量
     * @return \think\Paginator
     */
    public static function getLogList($where = [], $limit = 20)
    {
        return self::with(['user'])
                   ->where($where)
                   ->order('created_at', 'desc')
                   ->paginate($limit);
    }
    
    /**
     * 获取用户操作统计
     * @param int $userId
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function getUserStatistics($userId, $startDate = '', $endDate = '')
    {
        $query = self::where('user_id', $userId);
        
        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }
        
        $total = $query->count();
        $moduleStats = $query->group('module')->column('count(*) as count, module');
        $actionStats = $query->group('action')->column('count(*) as count, action');
        
        return [
            'total' => $total,
            'module_stats' => $moduleStats,
            'action_stats' => $actionStats,
        ];
    }
    
    /**
     * 获取系统操作统计
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function getSystemStatistics($startDate = '', $endDate = '')
    {
        $query = self::alias('ol')->join('users u', 'ol.user_id = u.id');
        
        if ($startDate) {
            $query->where('ol.created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->where('ol.created_at', '<=', $endDate . ' 23:59:59');
        }
        
        $total = $query->count();
        $userStats = $query->group('ol.user_id')
                          ->field('count(*) as count, u.real_name, u.username')
                          ->order('count', 'desc')
                          ->limit(10)
                          ->select();
        
        $moduleStats = self::where('created_at', '>=', $startDate ?: '1970-01-01')
                          ->where('created_at', '<=', $endDate ? $endDate . ' 23:59:59' : '2099-12-31')
                          ->group('module')
                          ->field('count(*) as count, module')
                          ->order('count', 'desc')
                          ->select();
        
        return [
            'total' => $total,
            'user_stats' => $userStats,
            'module_stats' => $moduleStats,
        ];
    }
    
    /**
     * 清理过期日志
     * @param int $days 保留天数
     * @return int 删除的记录数
     */
    public static function cleanExpiredLogs($days = 90)
    {
        $expireDate = date('Y-m-d H:i:s', time() - $days * 24 * 3600);
        return self::where('created_at', '<', $expireDate)->delete();
    }
    
    /**
     * 格式化操作描述
     * @return string
     */
    public function getFormattedDescription()
    {
        $user = $this->user;
        $userName = $user ? $user->real_name ?: $user->username : '未知用户';
        
        return sprintf('[%s] %s %s %s', 
            $this->created_at, 
            $userName, 
            $this->module, 
            $this->description ?: $this->action
        );
    }
}
