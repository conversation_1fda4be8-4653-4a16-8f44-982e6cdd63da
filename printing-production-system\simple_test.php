<?php
/**
 * 简单的登录测试
 */

echo "===========================================\n";
echo "简单登录测试\n";
echo "===========================================\n\n";

// 测试简单的POST请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/admin/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'username=admin&password=password');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

echo "HTTP状态码: $httpCode\n";
echo "内容类型: $contentType\n";

// 检查响应是否包含JSON
if (strpos($response, '{"code":') === 0) {
    echo "响应类型: JSON\n";
    echo "响应内容: $response\n";
} else {
    echo "响应类型: HTML\n";
    echo "响应长度: " . strlen($response) . " 字符\n";
    echo "响应开头: " . substr($response, 0, 200) . "...\n";
}

curl_close($ch);

echo "\n===========================================\n";
echo "测试完成\n";
echo "===========================================\n";
