<?php
require_once 'vendor/autoload.php';

$app = new \think\App();
$app->initialize();

use app\common\model\User;
use app\common\model\Process;
use app\common\model\Product;
use app\common\model\ProductionOrder;
use app\common\model\ProductionOrderProcess;

try {
    echo "===========================================\n";
    echo "模型功能测试\n";
    echo "===========================================\n\n";
    
    // 测试用户模型
    echo "1. 测试用户模型\n";
    echo "-------------------------------------------\n";
    $admin = User::getByUsername('admin');
    if ($admin) {
        echo "✓ 管理员用户: {$admin->real_name} ({$admin->username})\n";
        echo "  角色: {$admin->getRoleText()}\n";
        echo "  状态: {$admin->getStatusText()}\n";
    }
    
    $operators = User::getOperators();
    echo "✓ 操作员数量: " . count($operators) . "\n";
    foreach ($operators as $operator) {
        echo "  - {$operator->real_name} ({$operator->username})\n";
    }
    
    // 测试工序模型
    echo "\n2. 测试工序模型\n";
    echo "-------------------------------------------\n";
    $processes = Process::getActiveList();
    echo "✓ 启用工序数量: " . count($processes) . "\n";
    foreach ($processes as $process) {
        echo "  {$process->sort_order}. {$process->name} - {$process->getStatusText()}\n";
    }
    
    // 测试产品模型
    echo "\n3. 测试产品模型\n";
    echo "-------------------------------------------\n";
    $products = Product::getActiveList();
    echo "✓ 启用产品数量: " . count($products) . "\n";
    foreach ($products as $product) {
        echo "  - {$product->abbreviation}: {$product->name}\n";
        echo "    分类: {$product->category}, 价格: {$product->getFormattedPrice()}\n";
    }
    
    // 测试排产单模型
    echo "\n4. 测试排产单模型\n";
    echo "-------------------------------------------\n";
    $orders = ProductionOrder::with(['product', 'currentProcess'])->select();
    echo "✓ 排产单数量: " . count($orders) . "\n";
    foreach ($orders as $order) {
        echo "  - {$order->order_no}: {$order->product->name}\n";
        echo "    数量: {$order->quantity}, 状态: {$order->getStatusText()}\n";
        echo "    当前工序: " . ($order->currentProcess ? $order->currentProcess->name : '未开始') . "\n";
        echo "    进度: {$order->progress}%\n";
    }
    
    // 测试排产单工序模型
    echo "\n5. 测试排产单工序模型\n";
    echo "-------------------------------------------\n";
    $orderProcesses = ProductionOrderProcess::with(['productionOrder', 'process'])->select();
    echo "✓ 排产单工序数量: " . count($orderProcesses) . "\n";
    
    // 按排产单分组显示
    $groupedProcesses = [];
    foreach ($orderProcesses as $op) {
        $orderId = $op->production_order_id;
        if (!isset($groupedProcesses[$orderId])) {
            $groupedProcesses[$orderId] = [
                'order' => $op->productionOrder,
                'processes' => []
            ];
        }
        $groupedProcesses[$orderId]['processes'][] = $op;
    }
    
    foreach ($groupedProcesses as $group) {
        echo "  排产单: {$group['order']->order_no}\n";
        foreach ($group['processes'] as $op) {
            echo "    {$op->sort_order}. {$op->process->name} - {$op->getStatusText()}\n";
            if ($op->status === 'completed') {
                echo "       合格数量: {$op->qualified_quantity}, 合格率: {$op->qualification_rate}%\n";
            }
        }
    }
    
    // 测试产品搜索
    echo "\n6. 测试产品搜索功能\n";
    echo "-------------------------------------------\n";
    $searchResults = Product::searchByAbbreviation('HZ');
    echo "✓ 搜索 'HZ' 的结果数量: " . count($searchResults) . "\n";
    foreach ($searchResults as $product) {
        echo "  - {$product->abbreviation}: {$product->name}\n";
    }
    
    // 测试工序选项
    echo "\n7. 测试工序选项\n";
    echo "-------------------------------------------\n";
    $processOptions = Process::getProcessOptions();
    echo "✓ 工序选项:\n";
    foreach ($processOptions as $id => $name) {
        echo "  {$id}: {$name}\n";
    }
    
    echo "\n===========================================\n";
    echo "所有模型测试完成！\n";
    echo "===========================================\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
